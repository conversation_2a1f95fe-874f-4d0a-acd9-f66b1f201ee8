#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes for Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:110), pid=5840, tid=44272
#
# JRE version: OpenJDK Runtime Environment Corretto-*********.1 (17.0.10+7) (build 17.0.10+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Corretto-*********.1 (17.0.10+7-LTS, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:55415,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture14125013111986047790.props -Dvisualvm.id=88433417934700 -agentpath:C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder4136\libasyncProfiler.dll=version,jfr,event=wall,interval=10ms,cstack=no,file=C:\Users\<USER>\IdeaSnapshots\SmaileApplication_2025_09_12_090023.jfr,dbghelppath=C:\Users\<USER>\AppData\Local\Temp\idea_dbghelp_dll_temp_folder3\dbghelp.dll,log=C:\Users\<USER>\AppData\Local\Temp\SmaileApplication_2025_09_12_090023.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 com.smaile.health.SmaileApplication

Host: Intel(R) Core(TM) i7-14700, 28 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Fri Sep 12 09:00:24 2025 SE Asia Standard Time elapsed time: 0.595971 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000026e59f3b7f0):  JavaThread "main" [_thread_in_vm, id=44272, stack(0x000000f664d00000,0x000000f664e00000)]

Stack: [0x000000f664d00000,0x000000f664e00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x67d2c9]
V  [jvm.dll+0x834b1a]
V  [jvm.dll+0x8365de]
V  [jvm.dll+0x836c43]
V  [jvm.dll+0x2477ff]
V  [jvm.dll+0x8307db]
V  [jvm.dll+0x621d16]
V  [jvm.dll+0x621d7a]
V  [jvm.dll+0x6245aa]
V  [jvm.dll+0x6226b6]
V  [jvm.dll+0x24516c]
V  [jvm.dll+0x6d413d]
V  [jvm.dll+0x6d4b7f]
V  [jvm.dll+0x373644]
V  [jvm.dll+0x3726c4]
V  [jvm.dll+0x372668]
V  [jvm.dll+0x38264a]
C  0x0000026e6281582e

The last pc belongs to new (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  org.jetbrains.capture.org.objectweb.asm.ClassWriter.visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/jetbrains/capture/org/objectweb/asm/MethodVisitor;+0
j  org.jetbrains.capture.org.objectweb.asm.ClassVisitor.visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/jetbrains/capture/org/objectweb/asm/MethodVisitor;+18
j  com.intellij.rt.debugger.agent.ThrowableTransformer$1.visitMethod(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;)Lorg/jetbrains/capture/org/objectweb/asm/MethodVisitor;+8
j  org.jetbrains.capture.org.objectweb.asm.ClassReader.readMethod(Lorg/jetbrains/capture/org/objectweb/asm/ClassVisitor;Lorg/jetbrains/capture/org/objectweb/asm/Context;I)I+528
j  org.jetbrains.capture.org.objectweb.asm.ClassReader.accept(Lorg/jetbrains/capture/org/objectweb/asm/ClassVisitor;[Lorg/jetbrains/capture/org/objectweb/asm/Attribute;I)V+1397
j  org.jetbrains.capture.org.objectweb.asm.ClassReader.accept(Lorg/jetbrains/capture/org/objectweb/asm/ClassVisitor;I)V+7
j  com.intellij.rt.debugger.agent.ClassTransformer.accept(Lorg/jetbrains/capture/org/objectweb/asm/ClassVisitor;IZ)[B+6
j  com.intellij.rt.debugger.agent.ThrowableTransformer.transform(Ljava/lang/ClassLoader;Ljava/lang/String;Ljava/lang/Class;Ljava/security/ProtectionDomain;[B)[B+43
j  java.lang.instrument.ClassFileTransformer.transform(Ljava/lang/Module;Ljava/lang/ClassLoader;Ljava/lang/String;Ljava/lang/Class;Ljava/security/ProtectionDomain;[B)[B+9 java.instrument@17.0.10
j  sun.instrument.TransformerManager.transform(Ljava/lang/Module;Ljava/lang/ClassLoader;Ljava/lang/String;Ljava/lang/Class;Ljava/security/ProtectionDomain;[B)[B+52 java.instrument@17.0.10
j  sun.instrument.InstrumentationImpl.transform(Ljava/lang/Module;Ljava/lang/ClassLoader;Ljava/lang/String;Ljava/lang/Class;Ljava/security/ProtectionDomain;[BZ)[B+69 java.instrument@17.0.10
v  ~StubRoutines::call_stub
j  sun.instrument.InstrumentationImpl.retransformClasses0(J[Ljava/lang/Class;)V+0 java.instrument@17.0.10
j  sun.instrument.InstrumentationImpl.retransformClasses([Ljava/lang/Class;)V+29 java.instrument@17.0.10
j  com.intellij.rt.debugger.agent.CaptureAgent.init(Ljava/util/Properties;Ljava/lang/instrument/Instrumentation;)V+83
j  com.intellij.rt.debugger.agent.DebuggerAgent.initAll(Ljava/lang/instrument/Instrumentation;Ljava/util/Properties;)V+6
j  com.intellij.rt.debugger.agent.DebuggerAgent.readAndApplyProperties(Ljava/lang/String;Ljava/lang/instrument/Instrumentation;)V+104
j  com.intellij.rt.debugger.agent.DebuggerAgent.premain(Ljava/lang/String;Ljava/lang/instrument/Instrumentation;)V+44
v  ~StubRoutines::call_stub
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+0 java.base@17.0.10
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+133 java.base@17.0.10
j  jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+6 java.base@17.0.10
j  java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+59 java.base@17.0.10
j  sun.instrument.InstrumentationImpl.loadClassAndStartAgent(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V+167 java.instrument@17.0.10
j  sun.instrument.InstrumentationImpl.loadClassAndCallPremain(Ljava/lang/String;Ljava/lang/String;)V+6 java.instrument@17.0.10
v  ~StubRoutines::call_stub
new  187 new  [0x0000026e628156a0, 0x0000026e62815980]  736 bytes
[MachCode]
  0x0000026e628156a0: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x0000026e628156c0: 4424 0800 | 0000 00eb | 0150 410f | b755 010f | cac1 ea10 | 488b 4de8 | 488b 4908 | 488b 4908 
  0x0000026e628156e0: 488b 4108 | 807c 1004 | 070f 85df | 0000 0066 | 8b54 d148 | 488b 4928 | 488b 4cd1 | 0851 80b9 
  0x0000026e62815700: 3301 0000 | 040f 85c2 | 0000 008b | 5108 f7c2 | 0100 0000 | 0f85 b300 | 0000 498b | 8708 0100 
  0x0000026e62815720: 0048 8d1c | 1049 3b9f | 1801 0000 | 0f87 9b00 | 0000 4989 | 9f08 0100 | 00e9 0000 | 0000 4883 
  0x0000026e62815740: ea10 0f84 | 0f00 0000 | 33c9 c1ea | 0348 894c | d008 48ff | ca75 f648 | c700 0100 | 0000 5933 
  0x0000026e62815760: f689 700c | 49ba 0000 | 0000 6e02 | 0000 492b | ca89 4808 | 49ba 7a35 | 791c fd7f | 0000 4180 
  0x0000026e62815780: 3a00 0f84 | 4000 0000 | 5048 8bc8 | 4883 ec20 | f7c4 0f00 | 0000 0f84 | 1a00 0000 | 4883 ec08 
  0x0000026e628157a0: 49ba 2030 | 2f1c fd7f | 0000 41ff | d248 83c4 | 08e9 0d00 | 0000 49ba | 2030 2f1c | fd7f 0000 
  0x0000026e628157c0: 41ff d248 | 83c4 2058 | e992 0100 | 0059 488b | 55e8 488b | 5208 488b | 5208 450f | b745 0141 
  0x0000026e628157e0: 0fc8 41c1 | e810 e805 | 0000 00e9 | 6f01 0000 | 488d 4424 | 084c 896d | c049 8bcf | c5f8 7749 
  0x0000026e62815800: 89af a802 | 0000 4989 | 8798 0200 | 0048 83ec | 20f7 c40f | 0000 000f | 841a 0000 | 0048 83ec 
  0x0000026e62815820: 0849 bac0 | 25f9 1bfd | 7f00 0041 | ffd2 4883 | c408 e90d | 0000 0049 | bac0 25f9 | 1bfd 7f00 
  0x0000026e62815840: 0041 ffd2 | 4883 c420 | 49c7 8798 | 0200 0000 | 0000 0049 | c787 a802 | 0000 0000 | 0000 49c7 
  0x0000026e62815860: 87a0 0200 | 0000 0000 | 00c5 f877 | 418b 8f0c | 0400 00f7 | c101 0000 | 000f 8449 | 0000 00f7 
  0x0000026e62815880: c102 0000 | 000f 853d | 0000 0048 | 83ec 20f7 | c40f 0000 | 000f 841a | 0000 0048 | 83ec 0849 
  0x0000026e628158a0: ba50 00f9 | 1bfd 7f00 | 0041 ffd2 | 4883 c408 | e90d 0000 | 0049 ba50 | 00f9 1bfd | 7f00 0041 
  0x0000026e628158c0: ffd2 4883 | c420 ffe0 | 498b 8f90 | 0400 0048 | 85c9 0f84 | 5900 0000 | 8b89 8c00 | 0000 83f9 
  0x0000026e628158e0: 010f 854a | 0000 0049 | 8b8f 9004 | 0000 8b89 | 9000 0000 | 4883 ec20 | f7c4 0f00 | 0000 0f84 
  0x0000026e62815900: 1a00 0000 | 4883 ec08 | 49ba 4000 | f91b fd7f | 0000 41ff | d248 83c4 | 08e9 0d00 | 0000 49ba 
  0x0000026e62815920: 4000 f91b | fd7f 0000 | 41ff d248 | 83c4 20ff | e049 817f | 0800 0000 | 000f 8405 | 0000 00e9 
  0x0000026e62815940: bcb5 fdff | 498b 87f0 | 0200 0049 | c787 f002 | 0000 0000 | 0000 4c8b | 6dc0 4c8b | 75c8 c341 
  0x0000026e62815960: 0fb6 5d03 | 4983 c503 | 49ba 90a1 | 7b1c fd7f | 0000 41ff | 24da 6690 | 0000 0000 | 0000 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000026e7d4043d0, length=18, elements={
0x0000026e59f3b7f0, 0x0000026e7d083c70, 0x0000026e7d084a00, 0x0000026e7d09c8b0,
0x0000026e7d0a0300, 0x0000026e7d0a0bd0, 0x0000026e7d0a28b0, 0x0000026e7d0ab240,
0x0000026e7d0b1870, 0x0000026e7d1608d0, 0x0000026e7d065cb0, 0x0000026e7d0661d0,
0x0000026e7d2856c0, 0x0000026e7d285be0, 0x0000026e7d286510, 0x0000026e7d3f2ee0,
0x0000026e7d40d560, 0x0000026e7d40da60
}
_to_delete_list=0x0000026e7d404820, length=17, elements={
0x0000026e59f3b7f0, 0x0000026e7d083c70, 0x0000026e7d084a00, 0x0000026e7d09c8b0,
0x0000026e7d0a0300, 0x0000026e7d0a0bd0, 0x0000026e7d0a28b0, 0x0000026e7d0ab240,
0x0000026e7d0b1870, 0x0000026e7d1608d0, 0x0000026e7d065cb0, 0x0000026e7d0661d0,
0x0000026e7d2856c0, 0x0000026e7d285be0, 0x0000026e7d286510, 0x0000026e7d3f2ee0,
0x0000026e7d40d560
}

Java Threads: ( => current thread )
=>0x0000026e59f3b7f0 JavaThread "main" [_thread_in_vm, id=44272, stack(0x000000f664d00000,0x000000f664e00000)]
  0x0000026e7d083c70 JavaThread "Reference Handler" daemon [_thread_blocked, id=43252, stack(0x000000f665400000,0x000000f665500000)]
  0x0000026e7d084a00 JavaThread "Finalizer" daemon [_thread_blocked, id=24184, stack(0x000000f665500000,0x000000f665600000)]
  0x0000026e7d09c8b0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=15260, stack(0x000000f665600000,0x000000f665700000)]
  0x0000026e7d0a0300 JavaThread "Attach Listener" daemon [_thread_blocked, id=31752, stack(0x000000f665700000,0x000000f665800000)]
  0x0000026e7d0a0bd0 JavaThread "Service Thread" daemon [_thread_blocked, id=41760, stack(0x000000f665800000,0x000000f665900000)]
  0x0000026e7d0a28b0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=46976, stack(0x000000f665900000,0x000000f665a00000)]
  0x0000026e7d0ab240 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=26564, stack(0x000000f665a00000,0x000000f665b00000)]
  0x0000026e7d0b1870 JavaThread "Sweeper thread" daemon [_thread_blocked, id=43064, stack(0x000000f665b00000,0x000000f665c00000)]
  0x0000026e7d1608d0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=33924, stack(0x000000f665c00000,0x000000f665d00000)]
  0x0000026e7d065cb0 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=6304, stack(0x000000f665d00000,0x000000f665e00000)]
  0x0000026e7d0661d0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=23500, stack(0x000000f665e00000,0x000000f665f00000)]
  0x0000026e7d2856c0 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=42312, stack(0x000000f665f00000,0x000000f666000000)]
  0x0000026e7d285be0 JavaThread "C1 CompilerThread4" daemon [_thread_blocked, id=15816, stack(0x000000f666000000,0x000000f666100000)]
  0x0000026e7d286510 JavaThread "C1 CompilerThread5" daemon [_thread_blocked, id=27640, stack(0x000000f666100000,0x000000f666200000)]
  0x0000026e7d3f2ee0 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=31920, stack(0x000000f666200000,0x000000f666300000)]
  0x0000026e7d40d560 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=1508, stack(0x000000f666300000,0x000000f666400000)]
  0x0000026e7d40da60 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=27592, stack(0x000000f666400000,0x000000f666500000)]

Other Threads:
  0x0000026e7d080090 VMThread "VM Thread" [stack: 0x000000f665300000,0x000000f665400000] [id=36776]
  0x0000026e59ff27b0 GCTaskThread "GC Thread#0" [stack: 0x000000f664e00000,0x000000f664f00000] [id=40716]
  0x0000026e5a003720 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000f664f00000,0x000000f665000000] [id=4724]
  0x0000026e5a004660 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000f665000000,0x000000f665100000] [id=8864]
  0x0000026e7cf34590 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000f665100000,0x000000f665200000] [id=33688]
  0x0000026e5a0aac00 ConcurrentGCThread "G1 Service" [stack: 0x000000f665200000,0x000000f665300000] [id=28844]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000026e59f34f50] Metaspace_lock - owner thread: 0x0000026e59f3b7f0

Heap address: 0x0000000603800000, size: 8136 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000026e00000000-0x0000026e00bb0000-0x0000026e00bb0000), size 12255232, SharedBaseAddress: 0x0000026e00000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000026e01000000-0x0000026e41000000, reserved size: 1073741824
Narrow klass base: 0x0000026e00000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 28 total, 28 available
 Memory: 32535M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8136M
 Pre-touch: Disabled
 Parallel Workers: 20
 Concurrent Workers: 5
 Concurrent Refinement Workers: 20
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 4096K [0x0000000603800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 0 survivors (0K)
 Metaspace       used 480K, committed 640K, reserved 1114112K
  class space    used 33K, committed 128K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000603800000, 0x0000000603800000, 0x0000000603c00000|  0%| F|  |TAMS 0x0000000603800000, 0x0000000603800000| Untracked 
|   1|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000, 0x0000000603c00000| Untracked 
|   2|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000, 0x0000000604000000| Untracked 
|   3|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000, 0x0000000604400000| Untracked 
|   4|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000, 0x0000000604800000| Untracked 
|   5|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   6|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|   7|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|   8|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|   9|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|  10|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|  11|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|  12|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|  13|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  14|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  15|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  16|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  17|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
|  18|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  19|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  20|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  21|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  22|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  23|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  24|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  25|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Untracked 
|  26|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|  27|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000, 0x000000060a400000| Untracked 
|  28|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000, 0x000000060a800000| Untracked 
|  29|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000, 0x000000060ac00000| Untracked 
|  30|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000, 0x000000060b000000| Untracked 
|  31|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000, 0x000000060b400000| Untracked 
|  32|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000, 0x000000060b800000| Untracked 
|  33|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000, 0x000000060bc00000| Untracked 
|  34|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|  35|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000, 0x000000060c400000| Untracked 
|  36|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000, 0x000000060c800000| Untracked 
|  37|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000, 0x000000060cc00000| Untracked 
|  38|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000, 0x000000060d000000| Untracked 
|  39|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000, 0x000000060d400000| Untracked 
|  40|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000, 0x000000060d800000| Untracked 
|  41|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000, 0x000000060dc00000| Untracked 
|  42|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|  43|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000, 0x000000060e400000| Untracked 
|  44|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000, 0x000000060e800000| Untracked 
|  45|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000, 0x000000060ec00000| Untracked 
|  46|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000, 0x000000060f000000| Untracked 
|  47|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000, 0x000000060f400000| Untracked 
|  48|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000, 0x000000060f800000| Untracked 
|  49|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000, 0x000000060fc00000| Untracked 
|  50|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|  51|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000, 0x0000000610400000| Untracked 
|  52|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000, 0x0000000610800000| Untracked 
|  53|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000, 0x0000000610c00000| Untracked 
|  54|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000, 0x0000000611000000| Untracked 
|  55|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000, 0x0000000611400000| Untracked 
|  56|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000, 0x0000000611800000| Untracked 
|  57|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000, 0x0000000611c00000| Untracked 
|  58|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
|  59|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000, 0x0000000612400000| Untracked 
|  60|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000, 0x0000000612800000| Untracked 
|  61|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000, 0x0000000612c00000| Untracked 
|  62|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000, 0x0000000613000000| Untracked 
|  63|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000, 0x0000000613400000| Untracked 
|  64|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000, 0x0000000613800000| Untracked 
|  65|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000, 0x0000000613c00000| Untracked 
|  66|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
|  67|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000, 0x0000000614400000| Untracked 
|  68|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000, 0x0000000614800000| Untracked 
|  69|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000, 0x0000000614c00000| Untracked 
|  70|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000, 0x0000000615000000| Untracked 
|  71|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000, 0x0000000615400000| Untracked 
|  72|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000, 0x0000000615800000| Untracked 
|  73|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000, 0x0000000615c00000| Untracked 
|  74|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
|  75|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000, 0x0000000616400000| Untracked 
|  76|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000, 0x0000000616800000| Untracked 
|  77|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000, 0x0000000616c00000| Untracked 
|  78|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000, 0x0000000617000000| Untracked 
|  79|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000, 0x0000000617400000| Untracked 
|  80|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000, 0x0000000617800000| Untracked 
|  81|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000, 0x0000000617c00000| Untracked 
|  82|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
|  83|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000, 0x0000000618400000| Untracked 
|  84|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000, 0x0000000618800000| Untracked 
|  85|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000, 0x0000000618c00000| Untracked 
|  86|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000, 0x0000000619000000| Untracked 
|  87|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000, 0x0000000619400000| Untracked 
|  88|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000, 0x0000000619800000| Untracked 
|  89|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000, 0x0000000619c00000| Untracked 
|  90|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000, 0x000000061a000000| Untracked 
|  91|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000, 0x000000061a400000| Untracked 
|  92|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000, 0x000000061a800000| Untracked 
|  93|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000, 0x000000061ac00000| Untracked 
|  94|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000, 0x000000061b000000| Untracked 
|  95|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000, 0x000000061b400000| Untracked 
|  96|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000, 0x000000061b800000| Untracked 
|  97|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000, 0x000000061bc00000| Untracked 
|  98|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000, 0x000000061c000000| Untracked 
|  99|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000, 0x000000061c400000| Untracked 
| 100|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000, 0x000000061c800000| Untracked 
| 101|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000, 0x000000061cc00000| Untracked 
| 102|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000, 0x000000061d000000| Untracked 
| 103|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000, 0x000000061d400000| Untracked 
| 104|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000, 0x000000061d800000| Untracked 
| 105|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000, 0x000000061dc00000| Untracked 
| 106|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000, 0x000000061e000000| Untracked 
| 107|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000, 0x000000061e400000| Untracked 
| 108|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000, 0x000000061e800000| Untracked 
| 109|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000, 0x000000061ec00000| Untracked 
| 110|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000, 0x000000061f000000| Untracked 
| 111|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000, 0x000000061f400000| Untracked 
| 112|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000, 0x000000061f800000| Untracked 
| 113|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000, 0x000000061fc00000| Untracked 
| 114|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000, 0x0000000620000000| Untracked 
| 115|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000, 0x0000000620400000| Untracked 
| 116|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000, 0x0000000620800000| Untracked 
| 117|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000, 0x0000000620c00000| Untracked 
| 118|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 119|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000, 0x0000000621400000| Untracked 
| 120|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000, 0x0000000621800000| Untracked 
| 121|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000, 0x0000000621c00000| Untracked 
| 122|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000, 0x0000000622000000| Untracked 
| 123|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000, 0x0000000622400000| Untracked 
| 124|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000, 0x0000000622800000| Untracked 
| 125|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000, 0x0000000622c00000| Untracked 
| 126|0x0000000623000000, 0x00000006231712a0, 0x0000000623400000| 36%| E|  |TAMS 0x0000000623000000, 0x0000000623000000| Complete 
| 127|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000, 0x0000000623400000| Complete 

Card table byte_map: [0x0000026e67a00000,0x0000026e689f0000] _byte_map_base: 0x0000026e649e4000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000026e59ff2ce0, (CMBitMap*) 0x0000026e59ff2d20
 Prev Bits: [0x0000026e699e0000, 0x0000026e71900000)
 Next Bits: [0x0000026e71900000, 0x0000026e79820000)

Polling page: 0x0000026e58160000

Metaspace:

Usage:
  Non-class:    447.05 KB used.
      Class:     33.15 KB used.
       Both:    480.20 KB used.

Virtual space:
  Non-class space:       64.00 MB reserved,     512.00 KB ( <1%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     128.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,     640.00 KB ( <1%) committed. 

Chunk freelists:
   Non-Class:  12.00 MB
       Class:  15.75 MB
        Both:  27.75 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 4.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 10.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 5.
num_chunk_merges: 0.
num_chunk_splits: 3.
num_chunks_enlarged: 1.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=4256Kb max_used=4256Kb free=44895Kb
 bounds [0x0000026e627f0000, 0x0000026e62c20000, 0x0000026e657f0000]
 total_blobs=559 nmethods=224 adapters=258
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.594 Thread 0x0000026e7d0661d0  216       1       org.jetbrains.capture.org.objectweb.asm.ClassReader::readUnsignedShort (26 bytes)
Event: 0.594 Thread 0x0000026e7d065cb0  217       1       java.lang.String::checkBoundsOffCount (64 bytes)
Event: 0.594 Thread 0x0000026e7d0661d0 nmethod 216 0x0000026e62c14810 code [0x0000026e62c149a0, 0x0000026e62c14ad8]
Event: 0.594 Thread 0x0000026e7d2856c0  218       1       org.jetbrains.capture.org.objectweb.asm.ClassReader::readUtf (44 bytes)
Event: 0.594 Thread 0x0000026e7d285be0  219       1       org.jetbrains.capture.org.objectweb.asm.ClassReader::getItem (7 bytes)
Event: 0.594 Thread 0x0000026e7d286510  220       1       org.jetbrains.capture.org.objectweb.asm.ClassReader::readUTF8 (23 bytes)
Event: 0.594 Thread 0x0000026e7d0661d0  221       1       org.jetbrains.capture.org.objectweb.asm.ClassReader::readUtf (62 bytes)
Event: 0.594 Thread 0x0000026e7d285be0 nmethod 219 0x0000026e62c14c10 code [0x0000026e62c14da0, 0x0000026e62c14e98]
Event: 0.594 Thread 0x0000026e7d285be0  222       1       org.jetbrains.capture.org.objectweb.asm.Symbol::<init> (38 bytes)
Event: 0.594 Thread 0x0000026e7d286510 nmethod 220 0x0000026e62c14f90 code [0x0000026e62c15140, 0x0000026e62c152e8]
Event: 0.594 Thread 0x0000026e7d286510  223       1       org.jetbrains.capture.org.objectweb.asm.SymbolTable::add (39 bytes)
Event: 0.594 Thread 0x0000026e7d2856c0 nmethod 218 0x0000026e62c15490 code [0x0000026e62c15640, 0x0000026e62c15948]
Event: 0.594 Thread 0x0000026e7d2856c0  224       1       org.jetbrains.capture.org.objectweb.asm.SymbolTable::hash (10 bytes)
Event: 0.594 Thread 0x0000026e7d065cb0 nmethod 217 0x0000026e62c15c90 code [0x0000026e62c15ec0, 0x0000026e62c16318]
Event: 0.594 Thread 0x0000026e7d286510 nmethod 223 0x0000026e62c16890 code [0x0000026e62c16a40, 0x0000026e62c16cb8]
Event: 0.594 Thread 0x0000026e7d285be0 nmethod 222 0x0000026e62c16e90 code [0x0000026e62c17020, 0x0000026e62c17218]
Event: 0.594 Thread 0x0000026e7d065cb0  225       1       org.jetbrains.capture.org.objectweb.asm.SymbolTable$Entry::<init> (17 bytes)
Event: 0.594 Thread 0x0000026e7d0661d0 nmethod 221 0x0000026e62c17310 code [0x0000026e62c174c0, 0x0000026e62c176f8]
Event: 0.594 Thread 0x0000026e7d2856c0 nmethod 224 0x0000026e62c17990 code [0x0000026e62c17b20, 0x0000026e62c17c08]
Event: 0.594 Thread 0x0000026e7d065cb0 nmethod 225 0x0000026e62c17d10 code [0x0000026e62c17ea0, 0x0000026e62c17fe8]

GC Heap History (0 events):
No events

Dll operation events (4 events):
Event: 0.035 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\java.dll
Event: 0.036 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\zip.dll
Event: 0.060 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jsvml.dll
Event: 0.582 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.10\bin\instrument.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (6 events):
Event: 0.132 Executing VM operation: ChangeBreakpoints
Event: 0.132 Executing VM operation: ChangeBreakpoints done
Event: 0.580 Executing VM operation: ChangeBreakpoints
Event: 0.580 Executing VM operation: ChangeBreakpoints done
Event: 0.581 Executing VM operation: ChangeBreakpoints
Event: 0.581 Executing VM operation: ChangeBreakpoints done

Events (20 events):
Event: 0.593 loading class org/jetbrains/capture/org/objectweb/asm/ByteVector
Event: 0.593 loading class org/jetbrains/capture/org/objectweb/asm/ByteVector done
Event: 0.593 loading class org/jetbrains/capture/org/objectweb/asm/SymbolTable$Entry
Event: 0.593 loading class org/jetbrains/capture/org/objectweb/asm/Symbol
Event: 0.593 loading class org/jetbrains/capture/org/objectweb/asm/Symbol done
Event: 0.593 loading class org/jetbrains/capture/org/objectweb/asm/SymbolTable$Entry done
Event: 0.594 loading class com/intellij/rt/debugger/agent/ThrowableTransformer$1
Event: 0.594 loading class com/intellij/rt/debugger/agent/ThrowableTransformer$1 done
Event: 0.594 loading class org/jetbrains/capture/org/objectweb/asm/Attribute
Event: 0.594 loading class org/jetbrains/capture/org/objectweb/asm/Attribute done
Event: 0.594 loading class org/jetbrains/capture/org/objectweb/asm/Context
Event: 0.594 loading class org/jetbrains/capture/org/objectweb/asm/Context done
Event: 0.594 loading class org/jetbrains/capture/org/objectweb/asm/FieldWriter
Event: 0.594 loading class org/jetbrains/capture/org/objectweb/asm/FieldVisitor
Event: 0.594 loading class org/jetbrains/capture/org/objectweb/asm/FieldVisitor done
Event: 0.594 loading class org/jetbrains/capture/org/objectweb/asm/FieldWriter done
Event: 0.594 loading class org/jetbrains/capture/org/objectweb/asm/MethodWriter
Event: 0.594 loading class org/jetbrains/capture/org/objectweb/asm/MethodVisitor
Event: 0.594 loading class org/jetbrains/capture/org/objectweb/asm/MethodVisitor done
Event: 0.594 loading class org/jetbrains/capture/org/objectweb/asm/MethodWriter done


Dynamic libraries:
0x00007ff702070000 - 0x00007ff70207e000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\java.exe
0x00007ffdd8d50000 - 0x00007ffdd8f48000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffdd8980000 - 0x00007ffdd8a42000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffdd6600000 - 0x00007ffdd68f7000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffdd63e0000 - 0x00007ffdd64e0000 	C:\Windows\System32\ucrtbase.dll
0x00007ffd78cd0000 - 0x00007ffd78ce7000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jli.dll
0x00007ffd78c90000 - 0x00007ffd78cab000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\VCRUNTIME140.dll
0x00007ffdd8740000 - 0x00007ffdd88dd000 	C:\Windows\System32\USER32.dll
0x00007ffdd6be0000 - 0x00007ffdd6c02000 	C:\Windows\System32\win32u.dll
0x00007ffdbead0000 - 0x00007ffdbed6a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffdd7eb0000 - 0x00007ffdd7edb000 	C:\Windows\System32\GDI32.dll
0x00007ffdd8a50000 - 0x00007ffdd8aee000 	C:\Windows\System32\msvcrt.dll
0x00007ffdd6ac0000 - 0x00007ffdd6bd9000 	C:\Windows\System32\gdi32full.dll
0x00007ffdd64e0000 - 0x00007ffdd657d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffdd6ec0000 - 0x00007ffdd6eef000 	C:\Windows\System32\IMM32.DLL
0x00007ffda0570000 - 0x00007ffda057c000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\vcruntime140_1.dll
0x00007ffd772e0000 - 0x00007ffd7736d000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\msvcp140.dll
0x00007ffd1bc10000 - 0x00007ffd1c872000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\server\jvm.dll
0x00007ffdd7df0000 - 0x00007ffdd7ea1000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffdd88e0000 - 0x00007ffdd897f000 	C:\Windows\System32\sechost.dll
0x00007ffdd7cc0000 - 0x00007ffdd7de6000 	C:\Windows\System32\RPCRT4.dll
0x00007ffdd6990000 - 0x00007ffdd69b7000 	C:\Windows\System32\bcrypt.dll
0x00007ffda49d0000 - 0x00007ffda49d9000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffdd8af0000 - 0x00007ffdd8b5b000 	C:\Windows\System32\WS2_32.dll
0x00007ffdd17d0000 - 0x00007ffdd17da000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffdd58d0000 - 0x00007ffdd591b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffdc67f0000 - 0x00007ffdc6817000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffdd57a0000 - 0x00007ffdd57b2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffdd4160000 - 0x00007ffdd4172000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffda0500000 - 0x00007ffda050a000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jimage.dll
0x00007ffdc4d80000 - 0x00007ffdc4f81000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffda54e0000 - 0x00007ffda5514000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffdd6900000 - 0x00007ffdd6982000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffd78a80000 - 0x00007ffd78abb000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jdwp.dll
0x00007ffd9e860000 - 0x00007ffd9e86e000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\instrument.dll
0x00007ffd77a70000 - 0x00007ffd77a95000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\java.dll
0x00007ffd76240000 - 0x00007ffd76451000 	C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder4136\libasyncProfiler.dll
0x00007ffd78c70000 - 0x00007ffd78c88000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\zip.dll
0x00007ffd76040000 - 0x00007ffd76117000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\jsvml.dll
0x00007ffdd7440000 - 0x00007ffdd7bae000 	C:\Windows\System32\SHELL32.dll
0x00007ffdd4440000 - 0x00007ffdd4be4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffdd8040000 - 0x00007ffdd8393000 	C:\Windows\System32\combase.dll
0x00007ffdd5d30000 - 0x00007ffdd5d5b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffdd8670000 - 0x00007ffdd873d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffdd7bb0000 - 0x00007ffdd7c5d000 	C:\Windows\System32\SHCORE.dll
0x00007ffdd7f00000 - 0x00007ffdd7f5b000 	C:\Windows\System32\shlwapi.dll
0x00007ffdd6310000 - 0x00007ffdd6334000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffd9c800000 - 0x00007ffd9c80c000 	C:\Users\<USER>\.jdks\corretto-17.0.10\bin\dt_socket.dll
0x00007ffdd57c0000 - 0x00007ffdd57fb000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffdd5b10000 - 0x00007ffdd5b7a000 	C:\Windows\system32\mswsock.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\corretto-17.0.10\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Users\<USER>\.jdks\corretto-17.0.10\bin\server;C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder4136

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:55415,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture14125013111986047790.props -Dvisualvm.id=88433417934700 -agentpath:C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder4136\libasyncProfiler.dll=version,jfr,event=wall,interval=10ms,cstack=no,file=C:\Users\<USER>\IdeaSnapshots\SmaileApplication_2025_09_12_090023.jfr,dbghelppath=C:\Users\<USER>\AppData\Local\Temp\idea_dbghelp_dll_temp_folder3\dbghelp.dll,log=C:\Users\<USER>\AppData\Local\Temp\SmaileApplication_2025_09_12_090023.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.smaile.health.SmaileApplication
java_class_path (initial): E:\workspaces\eastgate\source\smaile\code\SMAILE\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.5.4\spring-boot-starter-web-3.5.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.5.4\spring-boot-starter-3.5.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.5.4\spring-boot-3.5.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.5.4\spring-boot-autoconfigure-3.5.4.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.5.4\spring-boot-starter-json-3.5.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.19.2\jackson-module-parameter-names-2.19.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.5.4\spring-boot-starter-tomcat-3.5.4.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.43\tomcat-embed-core-10.1.43.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.43\tomcat-embed-websocket-10.1.43.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.2.9\spring-web-6.2.9.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.2.9\spring-beans-6.2.9.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.2.9\spring-webmvc-6.2.9.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.2.9\spring-context-6.2.9.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.2.9\spring-expression-6.2.9.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\3.5.4\spring-boot-starter-validation-3.5.4.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.43\tomcat-embed-el-10.1.43.jar;C:\Users\<USER>\.m2\rep
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 5                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 20                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8531214336                                {product} {ergonomic}
   size_t MaxNewSize                               = 5117050880                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8531214336                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Users\<USER>\.jdks\corretto-17.0.10
PATH=C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\PuTTY\;C:\ProgramData\chocolatey\bin;C:\Windows\System32\OpenSSH\;C:\Program Files\Go\bin;C:\Program Files\Microsoft Network Monitor 3\;E:\workspaces\eastgate\softwares\apache-maven-3.9.1\bin;C:\Program Files\GitHub CLI\;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;E:\workspaces\setup\apache-ant-1.10.14\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Java\jdk-17\bin;C:\Program Files\Git\cmd;C:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\WireGuard\;C:\Program Files\TortoiseGit\bin;C:\Users\<USER>\AppData\Local\mise\shims;C:\Program Files\nodejs\;E:\workspaces\myspace\acli-rovo;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\PowerShell\7\;C:\Program Files (x86)\Scalefusion\Scalefusion MDM Agent\bin;E:\workspaces\eastgate\source\istio-1.27.0\bin;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\oh-my-posh\bin\;C:\Program Files (x86)\Microsoft SDKs\Azure\CLI2\wbin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\PuTTY\;C:\ProgramData\chocolatey\bin;C:\Windows\System32\OpenSSH\;C:\Program Files\Go\bin;C:\Program Files\Microsoft Network Monitor 3\;E:\workspaces\eastgate\softwares\apache-maven-3.9.1\bin;C:\Program Files\GitHub CLI\;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Program Files\nodejs\;E:\
USERNAME=Admin
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 1 days 0:33 hours
Hyper-V role detected

CPU: total 28 (initial active 28) (14 cores per cpu, 2 threads per core) family 6 model 183 stepping 1 microcode 0x12b, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv
Processor Information for all 28 processors :
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100

Memory: 4k page, system-wide physical 32535M (2083M free)
TotalPageFile size 57797M (AvailPageFile size 1M)
current process WorkingSet (physical memory assigned to process): 39M, peak: 39M
current process commit charge ("private bytes"): 645M, peak: 645M

vm_info: OpenJDK 64-Bit Server VM (17.0.10+7-LTS) for windows-amd64 JRE (17.0.10+7-LTS), built on Jan 10 2024 22:11:07 by "Administrator" with MS VC++ 16.10 / 16.11 (VS2019)

END.
