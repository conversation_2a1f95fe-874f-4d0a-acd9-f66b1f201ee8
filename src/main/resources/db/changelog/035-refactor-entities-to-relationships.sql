--liquibase formatted sql
--changeset trungnq:035-refactor-entities-to-relationships

-- Refactor entities to use proper relationships instead of JSON fields
-- Remove JSON columns from diagnosis table
ALTER TABLE diagnosis DROP COLUMN IF EXISTS authorized_specialties;
ALTER TABLE diagnosis DROP COLUMN IF EXISTS required_proofs;

-- Remove JSON columns from procedure table
ALTER TABLE procedure DROP COLUMN IF EXISTS authorized_specialties;

-- Add effective_date and cancellation_date columns to proofs table
ALTER TABLE proofs ADD COLUMN effective_date DATE NOT NULL DEFAULT CURRENT_DATE;
ALTER TABLE proofs ADD COLUMN cancellation_date DATE;

-- Update existing records to have a default effective_date
UPDATE proofs SET effective_date = CURRENT_DATE WHERE effective_date IS NULL;

-- Remove the default constraint after setting values
ALTER TABLE proofs ALTER COLUMN effective_date DROP DEFAULT;

--rollback <PERSON><PERSON>R TABLE proofs ALTER COLUMN effective_date SET DEFAULT CURRENT_DATE;
--rollback ALTER TABLE proofs DROP COLUMN IF EXISTS cancellation_date;
--rollback ALTER TABLE proofs DROP COLUMN IF EXISTS effective_date;
--rollback ALTER TABLE procedure ADD COLUMN authorized_specialties JSON;
--rollback ALTER TABLE diagnosis ADD COLUMN required_proofs JSON;
--rollback ALTER TABLE diagnosis ADD COLUMN authorized_specialties JSON;
