--liquibase formatted sql
--changeset audit-team:037-create-partitioned-audit-log

-- Drop existing audit_log table if exists (backup first in production!)
DROP TABLE IF EXISTS audit_log CASCADE;

-- Create partitioned parent table
CREATE TABLE audit_log (
    id BIGSERIAL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    entity_name VARCHAR(100) NOT NULL,
    entity_id VARCHAR(100) NOT NULL,
    operation VARCHAR(20) NOT NULL,
    old_values JSONB,
    new_values JSONB,
    changed_columns TEXT[],
    created_by VARCHAR(100) NOT NULL,
    
    PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (created_at);

-- Create initial partitions (current + next 3 months)
CREATE TABLE audit_log_y2025m09 PARTITION OF audit_log
    FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');

CREATE TABLE audit_log_y2025m10 PARTITION OF audit_log
    FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');

CREATE TABLE audit_log_y2025m11 PARTITION OF audit_log
    FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');

CREATE TABLE audit_log_y2025m12 PARTITION OF audit_log
    FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');

-- Create indexes on parent (inherited by partitions)
CREATE INDEX idx_audit_entity_time ON audit_log (entity_name, entity_id, created_at);
CREATE INDEX idx_audit_operation_time ON audit_log (operation, created_at);
CREATE INDEX idx_audit_user_time ON audit_log (created_by, created_at);

--rollback DROP FUNCTION IF EXISTS drop_old_audit_partitions(INTEGER);
--rollback DROP FUNCTION IF EXISTS create_monthly_audit_partition(DATE);
--rollback DROP TABLE IF EXISTS audit_log CASCADE;
