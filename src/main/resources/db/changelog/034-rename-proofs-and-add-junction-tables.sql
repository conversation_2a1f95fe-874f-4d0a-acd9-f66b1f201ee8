--liquibase formatted sql
--changeset trungnq:034-rename-proofs-and-add-junction-tables

-- Rename required_proofs table to proofs
ALTER TABLE required_proofs RENAME TO proofs;

-- Create diagnosis_speciality junction table
CREATE TABLE diagnosis_speciality
(
    diagnosis_id  UUID NOT NULL,
    speciality_id UUID NOT NULL,
    PRIMARY KEY (diagnosis_id, speciality_id),
    FOREIGN KEY (diagnosis_id) REFERENCES diagnosis (id) ON DELETE CASCADE,
    FOREIGN KEY (speciality_id) REFERENCES specialities (id) ON DELETE CASCADE
);

-- Create diagnosis_proof junction table
CREATE TABLE diagnosis_proof
(
    diagnosis_id UUID NOT NULL,
    proof_id     UUID NOT NULL,
    PRIMARY KEY (diagnosis_id, proof_id),
    FOREIGN KEY (diagnosis_id) REFERENCES diagnosis (id) ON DELETE CASCADE,
    FOREIGN KEY (proof_id) REFERENCES proofs (id) ON DELETE CASCADE
);

-- Create procedure_speciality junction table
CREATE TABLE procedure_speciality
(
    procedure_id  UUID NOT NULL,
    speciality_id UUID NOT NULL,
    <PERSON>IMARY KEY (procedure_id, speciality_id),
    FOREIGN KEY (procedure_id) REFERENCES procedure (id) ON DELETE CASCADE,
    FOREIGN KEY (speciality_id) REFERENCES specialities (id) ON DELETE CASCADE
);

-- Create procedure_proof junction table
CREATE TABLE procedure_proof
(
    procedure_id UUID NOT NULL,
    proof_id     UUID NOT NULL,
    PRIMARY KEY (procedure_id, proof_id),
    FOREIGN KEY (procedure_id) REFERENCES procedure (id) ON DELETE CASCADE,
    FOREIGN KEY (proof_id) REFERENCES proofs (id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_diagnosis_speciality_diagnosis_id ON diagnosis_speciality (diagnosis_id);
CREATE INDEX idx_diagnosis_speciality_speciality_id ON diagnosis_speciality (speciality_id);
CREATE INDEX idx_diagnosis_proof_diagnosis_id ON diagnosis_proof (diagnosis_id);
CREATE INDEX idx_diagnosis_proof_proof_id ON diagnosis_proof (proof_id);
CREATE INDEX idx_procedure_speciality_procedure_id ON procedure_speciality (procedure_id);
CREATE INDEX idx_procedure_speciality_speciality_id ON procedure_speciality (speciality_id);
CREATE INDEX idx_procedure_proof_procedure_id ON procedure_proof (procedure_id);
CREATE INDEX idx_procedure_proof_proof_id ON procedure_proof (proof_id);

--rollback DROP INDEX IF EXISTS idx_procedure_proof_proof_id;
--rollback DROP INDEX IF EXISTS idx_procedure_proof_procedure_id;
--rollback DROP INDEX IF EXISTS idx_procedure_speciality_speciality_id;
--rollback DROP INDEX IF EXISTS idx_procedure_speciality_procedure_id;
--rollback DROP INDEX IF EXISTS idx_diagnosis_proof_proof_id;
--rollback DROP INDEX IF EXISTS idx_diagnosis_proof_diagnosis_id;
--rollback DROP INDEX IF EXISTS idx_diagnosis_speciality_speciality_id;
--rollback DROP INDEX IF EXISTS idx_diagnosis_speciality_diagnosis_id;
--rollback DROP TABLE procedure_proof;
--rollback DROP TABLE procedure_speciality;
--rollback DROP TABLE diagnosis_proof;
--rollback DROP TABLE diagnosis_speciality;
--rollback ALTER TABLE proofs RENAME TO required_proofs;
