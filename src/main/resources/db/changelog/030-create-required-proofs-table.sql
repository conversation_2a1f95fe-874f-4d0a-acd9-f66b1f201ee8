--liquibase formatted sql
--changeset trungnq:030-create-required-proofs-table

-- Create required_proofs table
CREATE TABLE required_proofs
(
    id           UUID PRIMARY KEY,
    name         VARCHAR(255)             NOT NULL UNIQUE,
    description  TEXT,
    is_active    BOOLEAN                  NOT NULL DEFAULT TRUE,
    date_created TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by   VA<PERSON><PERSON><PERSON>(255)             NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_by   VARCHAR(255)             NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_required_proofs_name ON required_proofs (name);
CREATE INDEX idx_required_proofs_is_active ON required_proofs (is_active);

-- Add comments for documentation
COMMENT
ON TABLE required_proofs IS 'Stores required proofs/documents for diagnoses';
COMMENT
ON COLUMN required_proofs.name IS 'Name of the required proof (unique)';
COMMENT
ON COLUMN required_proofs.description IS 'Description of the proof requirement';
COMMENT
ON COLUMN required_proofs.is_active IS 'Whether the proof requirement is currently active';

-- Insert initial required proofs
INSERT INTO required_proofs (id, name, description, is_active, date_created, created_by, last_updated, updated_by)
VALUES (gen_random_uuid(), 'X-rays', 'Dental X-ray imaging for diagnosis', true, NOW(), 'system', NOW(), 'system'),
       (gen_random_uuid(), 'Photos', 'Clinical photographs of the oral condition', true, NOW(), 'system', NOW(),
        'system'),
       (gen_random_uuid(), 'Clinical Report', 'Detailed clinical examination report', true, NOW(), 'system', NOW(),
        'system'),
       (gen_random_uuid(), 'Referral', 'Referral letter from primary care physician', true, NOW(), 'system', NOW(),
        'system');

--rollback DELETE FROM required_proofs WHERE created_by = 'system';
--rollback DROP INDEX IF EXISTS idx_required_proofs_is_active;
--rollback DROP INDEX IF EXISTS idx_required_proofs_name;
--rollback DROP TABLE required_proofs;
