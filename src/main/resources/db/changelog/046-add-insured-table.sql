--liquibase formatted sql
--changeset longnh:044-add-insured-table

create table public.insureds
(
    id                   uuid                     not null
        constraint insureds_pk
            primary key,
    modality_id          uuid                     not null
        constraint insured_modalities_fk
            references modalities,
    ic_org_id            uuid                     not null
        constraint insured_organizations_fk
            references organizations,
    insured_code         varchar(255)             not null,
    first_effective_date timestamp with time zone,
    coverage_begin_date  timestamp with time zone not null,
    coverage_end_date    timestamp with time zone not null,
    status               varchar(100)             not null,
    status_date          timestamp with time zone,
    legal_id_type        varchar(100),
    legal_id_value       varchar(100),
    gender               varchar(255),
    date_of_birth        date,
    first_name           varchar(255),
    surname              varchar(255),
    last_name            varchar(255),
    is_deleted           boolean default false    not null,
    date_created         timestamp with time zone not null,
    last_updated         timestamp with time zone not null,
    created_by           varchar(255),
    updated_by           varchar(255),
    version              integer
);

--rollback drop table public.insureds;