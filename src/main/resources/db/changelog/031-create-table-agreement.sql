--liquibase formatted sql
--changeset maiph:026-create-table-agreement

CREATE TABLE agreements (
    id UUID NOT NULL,

    medical_provider_id UUID NOT NULL,

    contract_id VARCHAR(255) NOT NULL,
    short_description VARCHAR(255) NOT NULL,
    long_description TEXT NULL,
    effective_time TEXT NOT NULL,
    cancellation_time TEXT NOT NULL,
    status VARCHAR(50) NOT NULL,

    date_created    TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated    TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by varchar(255),
    updated_by varchar(255),

    PRIMARY KEY (id)
);

CREATE TABLE agreement_procedure_details (
    agreement_id UUID NOT NULL,
    procedure_id UUID NOT NULL,
    cost int NOT NULL,

    date_created    TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated    TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by var<PERSON><PERSON>(255),
    updated_by varchar(255),

    PRIMARY KEY (agreement_id, procedure_id),
    CONSTRAINT fk_agreement_procedure_details_agreement_id  FOREIGN KEY (agreement_id)  REFERENCES agreements(id) ON DELETE CASCADE
);
