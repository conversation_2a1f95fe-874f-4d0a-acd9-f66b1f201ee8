--liquibase formatted sql
--changeset trungnq:029-create-specialties-table

-- Create specialities table (unified table for both professional and authorized specialties)
CREATE TABLE specialities
(
    id           UUID PRIMARY KEY,
    name         VARCHAR(255)             NOT NULL UNIQUE,
    code         VARCHAR(255),
    description  TEXT,
    type         VARCHAR(50)              NOT NULL CHECK (type IN ('Professional', 'Authorize_Diagnosis')),
    is_active    BOOLEAN                  NOT NULL DEFAULT TRUE,
    date_created TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by   VARCHAR(255)             NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_by   VARCHAR(255)             NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_specialities_name ON specialities (name);
CREATE INDEX idx_specialities_type ON specialities (type);
CREATE INDEX idx_specialities_is_active ON specialities (is_active);

-- Insert initial authorized specialties
INSERT INTO specialities (id, name, description, type, is_active, date_created, created_by, last_updated, updated_by)
VALUES (gen_random_uuid(), 'General Dentists', 'General dental practice and basic dental care', 'Authorize_Diagnosis',
        true, NOW(), 'system', NOW(), 'system'),
       (gen_random_uuid(), 'Pedodontics / Pediatric Dentists', 'Dental care for children and adolescents',
        'Authorize_Diagnosis', true, NOW(), 'system', NOW(), 'system'),
       (gen_random_uuid(), 'Orthodontists', 'Braces and teeth alignment specialists', 'Authorize_Diagnosis', true,
        NOW(), 'system', NOW(), 'system'),
       (gen_random_uuid(), 'Periodontists', 'Gum disease treatment and surgery specialists', 'Authorize_Diagnosis',
        true, NOW(), 'system', NOW(), 'system'),
       (gen_random_uuid(), 'Endodontists', 'Root canal treatment and pulp therapy specialists', 'Authorize_Diagnosis',
        true, NOW(), 'system', NOW(), 'system'),
       (gen_random_uuid(), 'Oral Pathologists & Oral Surgeons', 'Diagnosis of oral diseases and surgical procedures',
        'Authorize_Diagnosis', true, NOW(), 'system', NOW(), 'system'),
       (gen_random_uuid(), 'Prosthodontists', 'Dental prosthetics and restoration specialists', 'Authorize_Diagnosis',
        true, NOW(), 'system', NOW(), 'system');

-- Migrate data from professional_specialties table
INSERT INTO specialities (id, name, code, type, is_active, date_created, created_by, last_updated, updated_by)
SELECT id,
       name,
       code,
       'Professional' as type,
       true           as is_active,
       date_created,
       created_by,
       last_updated,
       updated_by
FROM professional_specialties;

-- Drop the old professional_specialties table
DROP TABLE IF EXISTS professional_specialties;

--rollback CREATE TABLE professional_specialties (
--rollback     id UUID NOT NULL,
--rollback     name VARCHAR(255) NOT NULL,
--rollback     code VARCHAR(255) NOT NULL,
--rollback     version         BIGINT DEFAULT 0,
--rollback     date_created    TIMESTAMP WITH TIME ZONE NOT NULL,
--rollback     last_updated    TIMESTAMP WITH TIME ZONE NOT NULL,
--rollback     created_by varchar(255),
--rollback     updated_by varchar(255),
--rollback     PRIMARY KEY (id)
--rollback );
--rollback 
--rollback INSERT INTO professional_specialties (id, name, code, version, date_created, last_updated, created_by, updated_by)
--rollback SELECT id, name, code, 0, date_created, last_updated, created_by, updated_by
--rollback FROM specialities WHERE type = 'Professional';
--rollback 
--rollback DELETE FROM specialities WHERE created_by = 'system';
--rollback DROP INDEX IF EXISTS idx_specialities_is_active;
--rollback DROP INDEX IF EXISTS idx_specialities_type;
--rollback DROP INDEX IF EXISTS idx_specialities_name;
--rollback DROP TABLE specialities;
