--liquibase formatted sql
--changeset longnh:032-add-providers-professionals-table

create table public.providers_professionals
(
    id              uuid                     not null
        constraint providers_professionals_pk
            primary key,
    provider_id     uuid,
    professional_id uuid,
    status          varchar(20)              not null,
    version         integer,
    date_created    timestamp with time zone not null,
    created_by      varchar(255),
    last_updated    timestamp with time zone not null,
    updated_by      varchar(255),
    constraint providers_professionals_pk_2
        unique (professional_id, provider_id)
);

--rollback drop table public.providers_professionals;