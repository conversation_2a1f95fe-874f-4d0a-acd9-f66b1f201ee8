--liquibase formatted sql
--changeset trungnq:040-drop-type-and-code-columns

-- Drop the type column since it's no longer needed
ALTER TABLE specialities DROP COLUMN IF EXISTS type;

-- Drop the code column since it's no longer needed
ALTER TABLE specialities DROP COLUMN IF EXISTS code;

-- Drop the indexes on type and code columns since they no longer exist
DROP INDEX IF EXISTS idx_specialities_type;
DROP INDEX IF EXISTS idx_specialities_code;

--rollback ALTER TABLE specialities ADD COLUMN type VARCHAR(50);
--rollback ALTER TABLE specialities ADD COLUMN code VARCHAR(50);
--rollback CREATE INDEX idx_specialities_type ON specialities (type);
--rollback CREATE INDEX idx_specialities_code ON specialities (code);
--rollback UPDATE specialities SET type = 'Professional', code = 'PROFESSIONAL';
