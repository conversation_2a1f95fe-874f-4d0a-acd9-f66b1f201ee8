--liquibase formatted sql
--changeset longnh:047-alter-insured-datetime-to-date

alter table insureds alter column first_effective_date type date using first_effective_date::date;
alter table insureds alter column coverage_begin_date type date using coverage_begin_date::date;
alter table insureds alter column coverage_end_date type date using coverage_end_date::date;
alter table insureds alter column status_date type date using status_date::date;
alter table insureds add status_reason varchar;

--rollback alter table insureds drop column status_reason;
--rollback alter table insureds alter column first_effective_date type timestamptz using first_effective_date::timestamptz;
--rollback alter table insureds alter column coverage_begin_date type timestamptz using coverage_begin_date::timestamptz;
--rollback alter table insureds alter column coverage_end_date type timestamptz using coverage_end_date::timestamptz;
--rollback alter table insureds alter column status_date type timestamptz using status_date::timestamptz;