--liquibase formatted sql
--changeset maiph:043-ic-agreement-task

CREATE TABLE ic_agreements
(
    id                              UUID PRIMARY KEY,
    agreement_id                    UUID NOT NULL,
    insurance_company_id            UUID NOT NULL,
    effective_time                  TIMESTAMP WITH TIME ZONE NOT NULL,
    cancellation_time               TIMESTAMP WITH TIME ZONE NOT NULL,
    status                          varchar(255),
    version                         BIGINT DEFAULT 0,
    date_created                    TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated                    TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by                      varchar(255),
    updated_by                      varchar(255),
    CONSTRAINT unique_ic_agreements_agreement_id_insurance_company_id UNIQUE (agreement_id, insurance_company_id)
);

--R<PERSON><PERSON><PERSON>CK DROP TABLE ic_agreements;