--liquibase formatted sql
--changeset maiph:037-add-table-professional-specialty

CREATE TABLE professional_speciality
(
    speciality_id UUID NOT NULL,
    professional_id UUID NOT NULL,
    version         BIGINT DEFAULT 0,
    date_created timestamp with time zone NOT NULL,
    last_updated timestamp with time zone NOT NULL,
    created_by varchar(255),
    updated_by varchar(255),
    PRIMARY KEY(speciality_id, professional_id)
);

--changeset maiph:037-add-table-audit-log

CREATE TABLE audit_logs
(
    id UUID NOT NULL,
    timestamp_action timestamp with time zone NOT NULL,
    entity varchar(255),
    action varchar(255),
    entity_id UUID NOT NULL,
    actor varchar(255),
    description text,
    version         BIGINT DEFAULT 0,
    date_created timestamp with time zone NOT NULL,
    last_updated timestamp with time zone NOT NULL,
    created_by varchar(255),
    updated_by varchar(255),
    PRIMARY KEY(id)
);