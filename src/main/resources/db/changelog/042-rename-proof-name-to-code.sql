--liquibase formatted sql
--changeset trungnq:042-rename-proof-name-to-code

-- Rename name column to code in proofs table
ALTER TABLE proofs RENAME COLUMN name TO code;

-- Update the index name to reflect the column change
DROP INDEX IF EXISTS idx_required_proofs_name;
CREATE INDEX idx_proofs_code ON proofs (code);

-- Update comments for documentation
COMMENT
ON COLUMN proofs.code IS 'Code of the proof (unique)';

--rollback ALTER TABLE proofs RENAME COLUMN code TO name;
--rollback DROP INDEX IF EXISTS idx_proofs_code;
--rollback CREATE INDEX idx_required_proofs_name ON proofs (name);
--rollback COMMENT ON COLUMN proofs.name IS 'Name of the required proof (unique)';