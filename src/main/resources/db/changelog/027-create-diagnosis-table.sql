--liquibase formatted sql
--changeset trungnq:027-create-diagnosis-table

-- Create diagnosis table
CREATE TABLE diagnosis
(
    id                     UUID PRIMARY KEY,
    code                   VARCHAR(255)             NOT NULL UNIQUE,
    frequency              VARCHAR(10)              NOT NULL,
    description            TEXT                     NOT NULL,
    mouth_part_type        VARCHAR(20)              NOT NULL,
    mouth_part_id          VARCHAR(255),
    market_cost            DECIMAL(10, 2)           NOT NULL,
    knowledge_cost         DECIMAL(10, 2)           NOT NULL,
    effective_date         DATE                     NOT NULL,
    cancellation_date      DATE,
    authorized_specialties JSON,
    required_proofs        JSON,
    is_active              BOOLEAN                  NOT NULL DEFAULT TRUE,
    date_created           TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by             VA<PERSON>HAR(255)             NOT NULL,
    last_updated           TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_by             VARCHAR(255)             NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_diagnosis_code ON diagnosis (code);
CREATE INDEX idx_diagnosis_frequency ON diagnosis (frequency);
CREATE INDEX idx_diagnosis_mouth_part_type ON diagnosis (mouth_part_type);
CREATE INDEX idx_diagnosis_effective_date ON diagnosis (effective_date);
CREATE INDEX idx_diagnosis_is_active ON diagnosis (is_active);


--rollback DROP INDEX IF EXISTS idx_diagnosis_is_active;
--rollback DROP INDEX IF EXISTS idx_diagnosis_effective_date;
--rollback DROP INDEX IF EXISTS idx_diagnosis_mouth_part_type;
--rollback DROP INDEX IF EXISTS idx_diagnosis_frequency;
--rollback DROP INDEX IF EXISTS idx_diagnosis_code;
--rollback DROP TABLE diagnosis;
