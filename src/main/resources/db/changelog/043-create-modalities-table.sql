--liquibase formatted sql
--changeset phiendq:043-create-modalities-table.sql

CREATE TABLE modalities
(
    id                  UUID                     NOT NULL
        PRIMARY KEY,
    ic_org_id           UUID                     NOT NULL
        CONSTRAINT modalities_organizations_id_fk
            REFERENCES organizations,
    code                VARCHAR(255)             NOT NULL,
    short_name          VA<PERSON>HAR(255)             NOT NULL,
    name                VARCHAR(255)             NOT NULL,
    network_type        VARCHAR(50)              NOT NULL,
    annual_limit_global NUMERIC(18, 2),
    annual_limit_in     NUMERIC(18, 2),
    annual_limit_out    NUMERIC(18, 2),
    date_created        TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by          <PERSON><PERSON><PERSON><PERSON>(255)             NOT NULL,
    last_updated        TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_by          <PERSON><PERSON><PERSON><PERSON>(255)             NOT NULL
);

CREATE UNIQUE INDEX modalities_ic_org_id_code_uindex
    ON modalities (ic_org_id, code);

CREATE INDEX modalities_code_index
    ON modalities (code);

CREATE INDEX modalities_name_index
    ON modalities (name);

CREATE INDEX modalities_short_name_index
    ON modalities (short_name);


