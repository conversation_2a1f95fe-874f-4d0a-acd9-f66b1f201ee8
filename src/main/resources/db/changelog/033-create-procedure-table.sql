--liquibase formatted sql
--changeset trungnq:033-create-procedure-table

-- Create procedure table
CREATE TABLE procedure
(
    id                      UUID PRIMARY KEY,
    code                    VARCHAR(255)             NOT NULL UNIQUE,
    country                 VARCHAR(10)              NOT NULL,
    frequency               VARCHAR(10)              NOT NULL,
    description             TEXT                     NOT NULL,
    age_group               VARCHAR(10)              NOT NULL,
    market_cost             DECIMAL(10, 2)           NOT NULL,
    mouth_part_requirements JSON,
    effective_date          DATE                     NOT NULL,
    cancellation_date       DATE,
    authorized_specialties  JSON,
    is_active               BOOLEAN                  NOT NULL DEFAULT TRUE,
    date_created            TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by              VA<PERSON>HAR(255)             NOT NULL,
    last_updated            TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_by              VA<PERSON>HAR(255)             NOT NULL
);

-- Create procedure_diagnosis junction table for many-to-many relationship
CREATE TABLE procedure_diagnosis
(
    procedure_id UUID NOT NULL,
    diagnosis_id UUID NOT NULL,
    PRIMARY KEY (procedure_id, diagnosis_id),
    FOREIGN KEY (procedure_id) REFERENCES procedure (id) ON DELETE CASCADE,
    FOREIGN KEY (diagnosis_id) REFERENCES diagnosis (id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_procedure_code ON procedure (code);
CREATE INDEX idx_procedure_country ON procedure (country);
CREATE INDEX idx_procedure_frequency ON procedure (frequency);
CREATE INDEX idx_procedure_age_group ON procedure (age_group);
CREATE INDEX idx_procedure_effective_date ON procedure (effective_date);
CREATE INDEX idx_procedure_is_active ON procedure (is_active);
CREATE INDEX idx_procedure_diagnosis_procedure_id ON procedure_diagnosis (procedure_id);
CREATE INDEX idx_procedure_diagnosis_diagnosis_id ON procedure_diagnosis (diagnosis_id);

--rollback DROP INDEX IF EXISTS idx_procedure_diagnosis_diagnosis_id;
--rollback DROP INDEX IF EXISTS idx_procedure_diagnosis_procedure_id;
--rollback DROP INDEX IF EXISTS idx_procedure_is_active;
--rollback DROP INDEX IF EXISTS idx_procedure_effective_date;
--rollback DROP INDEX IF EXISTS idx_procedure_age_group;
--rollback DROP INDEX IF EXISTS idx_procedure_frequency;
--rollback DROP INDEX IF EXISTS idx_procedure_country;
--rollback DROP INDEX IF EXISTS idx_procedure_code;
--rollback DROP TABLE procedure_diagnosis;
--rollback DROP TABLE procedure;