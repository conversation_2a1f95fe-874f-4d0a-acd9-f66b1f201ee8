--liquibase formatted sql
--changeset phiendq:044-add-modality-permissions
INSERT INTO public.permissions (id, "name", resource, sub_resource, "action", description, "version", date_created, last_updated, created_by, updated_by) VALUES('96d7cb7c-ae49-40b4-a849-e0dd27e7e9fe'::uuid, 'CREATE_MODALITY', 'modalities', '*', 'create', 'Create modality', 0, now(), now(), NULL, NULL);
INSERT INTO public.permissions (id, "name", resource, sub_resource, "action", description, "version", date_created, last_updated, created_by, updated_by) VALUES('c0138607-0663-4ab7-8a2b-9ac25b217ab5'::uuid, 'READ_MODALITY', 'modalities', '*', 'read', 'Read modality', 0, now(), now(), NULL, NULL);
INSERT INTO public.permissions (id, "name", resource, sub_resource, "action", description, "version", date_created, last_updated, created_by, updated_by) VALUES('f56923f1-3930-498f-8594-0ce791a270f8'::uuid, 'UPDATE_MODALITY', 'modalities', '*', 'update', 'Update modality', 0, now(), now(), NULL, NULL);
INSERT INTO public.permissions (id, "name", resource, sub_resource, "action", description, "version", date_created, last_updated, created_by, updated_by) VALUES('f4e4b3bb-7f2f-4c9c-bf1d-802131efe0d6'::uuid, 'DELETE_MODALITY', 'modalities', '*', 'delete', 'Delete modality', 0, now(), now(), NULL, NULL);

INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('efc928ad-0b57-4c47-a69d-061c2edbca42'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa26'::uuid, '96d7cb7c-ae49-40b4-a849-e0dd27e7e9fe'::uuid, 0, now(), now(), NULL, NULL);
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('c34ae232-b35a-4026-81d5-be6ca8e02581'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa26'::uuid, 'c0138607-0663-4ab7-8a2b-9ac25b217ab5'::uuid, 0, now(), now(), NULL, NULL);
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('6e7bfd98-be98-4f24-a5a9-e82642e3b309'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa26'::uuid, 'f56923f1-3930-498f-8594-0ce791a270f8'::uuid, 0, now(), now(), NULL, NULL);
INSERT INTO public.roles_permissions (id, role_id, permission_id, "version", date_created, last_updated, created_by, updated_by) VALUES('c92b7378-4a5e-4a7f-b952-0b0fa0008a86'::uuid, '62d490f5-fa15-4688-963d-c4e64271fa26'::uuid, 'f4e4b3bb-7f2f-4c9c-bf1d-802131efe0d6'::uuid, 0, now(), now(), NULL, NULL);



--rollback DELETE FROM public.roles_permissions WHERE id='efc928ad-0b57-4c47-a69d-061c2edbca42'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='c34ae232-b35a-4026-81d5-be6ca8e02581'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='6e7bfd98-be98-4f24-a5a9-e82642e3b309'::uuid;
--rollback DELETE FROM public.roles_permissions WHERE id='c92b7378-4a5e-4a7f-b952-0b0fa0008a86'::uuid;

--rollback DELETE FROM public.permissions WHERE id='96d7cb7c-ae49-40b4-a849-e0dd27e7e9fe'::uuid;
--rollback DELETE FROM public.permissions WHERE id='c0138607-0663-4ab7-8a2b-9ac25b217ab5'::uuid;
--rollback DELETE FROM public.permissions WHERE id='f56923f1-3930-498f-8594-0ce791a270f8'::uuid;
--rollback DELETE FROM public.permissions WHERE id='f4e4b3bb-7f2f-4c9c-bf1d-802131efe0d6'::uuid;