package com.smaile.health.endpoint.validation.annotation;

import com.smaile.health.endpoint.validation.validator.ValidDateRangeValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = ValidDateRangeValidator.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidDateRange {
    String message() default "error.proof.date-range-invalid";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    String effectiveDateField() default "effectiveDate";

    String cancellationDateField() default "cancellationDate";
}
