package com.smaile.health.endpoint.validation.validator;

import com.smaile.health.endpoint.validation.annotation.ValidDateRange;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.PropertyAccessorFactory;

import java.time.LocalDate;

public class ValidDateRangeValidator implements ConstraintValidator<ValidDateRange, Object> {

    private String effectiveDateField;
    private String cancellationDateField;

    @Override
    public void initialize(ValidDateRange constraintAnnotation) {
        this.effectiveDateField = constraintAnnotation.effectiveDateField();
        this.cancellationDateField = constraintAnnotation.cancellationDateField();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }

        BeanWrapper beanWrapper = PropertyAccessorFactory.forBeanPropertyAccess(value);

        LocalDate effectiveDate = (LocalDate) beanWrapper.getPropertyValue(effectiveDateField);
        LocalDate cancellationDate = (LocalDate) beanWrapper.getPropertyValue(cancellationDateField);

        // If either date is null, validation passes (let other validations handle null checks)
        if (effectiveDate == null || cancellationDate == null) {
            return true;
        }

        // Cancellation date cannot be before effective date
        return !cancellationDate.isBefore(effectiveDate);
    }
}
