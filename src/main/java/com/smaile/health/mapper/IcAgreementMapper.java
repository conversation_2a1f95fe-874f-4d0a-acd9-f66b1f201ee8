package com.smaile.health.mapper;

import com.smaile.health.domain.Agreement;
import com.smaile.health.domain.IcAgreement;
import com.smaile.health.model.AgreementDTO;
import com.smaile.health.model.InsuranceCompanyAgreementDTO;
import com.smaile.health.model.request.CreateAgreementDTO;
import com.smaile.health.model.request.CreateInsuranceCompanyAgreementRequest;
import com.smaile.health.model.request.UpdateInsuranceCompanyAgreementRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface IcAgreementMapper {

    InsuranceCompanyAgreementDTO toDTO(IcAgreement icAgreement);

    IcAgreement toEntity(CreateInsuranceCompanyAgreementRequest request);
    IcAgreement toEntity(UpdateInsuranceCompanyAgreementRequest request);

}