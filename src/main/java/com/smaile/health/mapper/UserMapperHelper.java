package com.smaile.health.mapper;

import com.smaile.health.domain.Organization;
import com.smaile.health.domain.Role;
import com.smaile.health.domain.User;
import com.smaile.health.domain.UserRole;
import com.smaile.health.exception.SmaileRuntimeException;

import java.util.Comparator;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

public class UserMapperHelper {
    private UserMapperHelper() {
        throw new UnsupportedOperationException();
    }

    public static String getPrimaryRoleCode(User user) {
        return getPrimaryRole(user).getCode();
    }

    public static String getPrimaryRoleDescription(User user) {
        return getPrimaryRole(user).getDescription();
    }

    private static Role getPrimaryRole (User user) {
        if (user == null) {
            throw new SmaileRuntimeException("User must not be null");
        }
        UUID primaryOrganizationId = Optional.of(user)
                .map(User::getOrganization)
                .map(Organization::getId)
                .orElseThrow(() -> new RuntimeException("Cannot get primary organization for user %s".formatted(user.getId().toString())));

        return user.getUserOrganizations().stream()
                .filter(uo -> uo.getOrganization() != null)
                .filter(uo -> Objects.equals(uo.getOrganization().getId(), primaryOrganizationId))
                .flatMap(uo -> uo.getUserRoles().stream())
                .min(Comparator.comparing(UserRole::getDateCreated))
                .map(UserRole::getRole)
                .orElseThrow(() -> new SmaileRuntimeException("Cannot get primary role for user %s".formatted(user.getEmail())));
    }
}
