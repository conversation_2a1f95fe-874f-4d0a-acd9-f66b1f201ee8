package com.smaile.health.mapper;

import com.smaile.health.domain.AuthorizedSpecialty;
import com.smaile.health.model.request.CreateAuthorizedSpecialtyRequestDTO;
import com.smaile.health.model.AuthorizedSpecialtyDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AuthorizedSpecialtyMapper {

    AuthorizedSpecialtyDTO toDTO(AuthorizedSpecialty authorizedSpecialty);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "isActive", constant = "true")
    AuthorizedSpecialty toEntity(CreateAuthorizedSpecialtyRequestDTO requestDTO);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "dateCreated", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastUpdated", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    void updateEntityFromDTO(AuthorizedSpecialtyDTO dto, @MappingTarget AuthorizedSpecialty authorizedSpecialty);
}
