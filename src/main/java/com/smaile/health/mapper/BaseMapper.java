package com.smaile.health.mapper;

import com.smaile.health.domain.BaseEntity;
import com.smaile.health.model.AuditDTO;
import com.smaile.health.service.UserService;
import org.mapstruct.AfterMapping;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.UUID;

public abstract class BaseMapper {

    @Autowired
    protected UserService userService;

    /**
     Get user name by UUID, with fallback to UUID string if user not found
     */
    protected String getUserName(UUID userId) {
        if (userId == null) {
            return null;
        }

        try {
            return userService.getUserName(userId);
        } catch (Exception e) {
            // Fallback to UUID string if user service fails
            return userId.toString();
        }
    }


    /**
     Generic method to set user names for any entity that extends BaseEntity
     This method will be called automatically by MapStruct for all mappers extending BaseMapper
     */
    @AfterMapping
    protected void setUserNames(BaseEntity entity, @MappingTarget AuditDTO dto) {
        if (entity == null || dto == null) {
            return;
        }

        // Set user names instead of UUIDs
        if (entity.getCreatedBy() != null) {
            try {
                UUID createdByUuid = UUID.fromString(entity.getCreatedBy());
                dto.setCreatedBy(getUserName(createdByUuid));
            } catch (IllegalArgumentException e) {
                // If createdBy is not a valid UUID, keep as is
                dto.setCreatedBy(entity.getCreatedBy());
            }
        }

        if (entity.getUpdatedBy() != null) {
            try {
                UUID updatedByUuid = UUID.fromString(entity.getUpdatedBy());
                dto.setUpdatedBy(getUserName(updatedByUuid));
            } catch (IllegalArgumentException e) {
                // If updatedBy is not a valid UUID, keep as is
                dto.setUpdatedBy(entity.getUpdatedBy());
            }
        }
    }
}
