package com.smaile.health.mapper;

import com.smaile.health.domain.Agreement;
import com.smaile.health.domain.Professional;
import com.smaile.health.model.AgreementDTO;
import com.smaile.health.model.AgreementSimpleInfoDTO;
import com.smaile.health.model.ProfessionalDTO;
import com.smaile.health.model.request.CreateAgreementDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AgreementMapper {

    AgreementDTO toDTO(Agreement agreement);

    AgreementSimpleInfoDTO toSimpleDTO(Agreement agreement);

    Agreement toEntity(AgreementDTO agreementDTO);

    Agreement toEntity(CreateAgreementDTO agreementDTO);

}