package com.smaile.health.mapper;

import com.smaile.health.domain.Insured;
import com.smaile.health.domain.InsuredQueryProjection;
import com.smaile.health.model.InsuredDTO;
import com.smaile.health.model.request.CreateInsuredRequest;
import com.smaile.health.model.request.UpdateInsuredRequest;
import com.smaile.health.model.response.InsuredResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE
)
public interface InsuredMapper {
    InsuredDTO toDTO(CreateInsuredRequest request);
    InsuredDTO toDTO(UpdateInsuredRequest request);

    @Mapping(target = "modality", ignore = true)
    @Mapping(target = "icOrg", ignore = true)
    InsuredDTO toDTO(Insured entity);

    @Mapping(source = "modalityId", target = "modality.id")
    @Mapping(source = "icOrgId", target = "icOrg.id")
    Insured toEntity(InsuredDTO dto);

    void update(InsuredDTO dto, @MappingTarget Insured entity);

    InsuredResponse toResponse(InsuredDTO dto);

    @Mapping(source = "insured", target = ".")
    @Mapping(source = "modalityName", target = "modalityName")
    @Mapping(target = "modality", ignore = true)
    @Mapping(target = "icOrg", ignore = true)
    @Mapping(source = "updatedBy", target = "updatedBy")
    InsuredResponse toResponse(InsuredQueryProjection projection);
}
