package com.smaile.health.mapper;

import com.smaile.health.domain.MedicalProvider;
import com.smaile.health.model.MedicalProviderDTO;
import com.smaile.health.model.MedicalProviderSimpleInfoDTO;
import com.smaile.health.model.request.CreateMedicalProviderRequest;
import com.smaile.health.model.request.UpdateMedicalProviderRequest;
import com.smaile.health.model.response.MedicalProviderResponse;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.WARN)
public interface MedicalProviderMapper {
    MedicalProviderResponse toResponse(MedicalProviderDTO dto);

    MedicalProviderDTO toDTO(CreateMedicalProviderRequest request);
    MedicalProviderSimpleInfoDTO toSimpleDTO(MedicalProvider entity);
    MedicalProviderDTO toDTO(UpdateMedicalProviderRequest request);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateEntity(MedicalProviderDTO dto, @MappingTarget MedicalProvider entity);

    @Mapping(target = "parentId", expression = "java(entity.getParent() == null ? null : entity.getParent().getId())")
    MedicalProviderDTO toDTO(MedicalProvider entity);

    MedicalProvider toEntity(MedicalProviderDTO dto);
}
