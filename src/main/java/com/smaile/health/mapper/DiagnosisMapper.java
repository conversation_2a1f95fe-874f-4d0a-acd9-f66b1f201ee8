package com.smaile.health.mapper;

import com.smaile.health.domain.Diagnosis;
import com.smaile.health.model.request.CreateDiagnosisRequestDTO;
import com.smaile.health.model.DiagnosisDTO;
import com.smaile.health.model.DiagnosisSummaryDTO;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {SpecialityMapper.class, ProofMapper.class})
public abstract class DiagnosisMapper extends BaseMapper {

    public abstract DiagnosisDTO toDTO(Diagnosis diagnosis);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "isActive", constant = "true")
    public abstract Diagnosis toEntity(CreateDiagnosisRequestDTO requestDTO);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "dateCreated", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastUpdated", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    public abstract void updateEntityFromDTO(DiagnosisDTO dto, @MappingTarget Diagnosis diagnosis);

    public abstract DiagnosisSummaryDTO toSummaryDTO(Diagnosis diagnosis);

}
