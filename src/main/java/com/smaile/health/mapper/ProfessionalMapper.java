package com.smaile.health.mapper;

import com.smaile.health.domain.InsuranceCompany;
import com.smaile.health.domain.Professional;
import com.smaile.health.domain.User;
import com.smaile.health.model.InsuranceCompanyDTO;
import com.smaile.health.model.ProfessionalDTO;
import com.smaile.health.model.UserDTO;
import com.smaile.health.model.request.CreateUserRequest;
import com.smaile.health.model.request.UpdateUserRequest;
import com.smaile.health.model.response.UserResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProfessionalMapper {

    ProfessionalDTO toDTO(Professional professional);

    Professional toEntity(ProfessionalDTO professionalDTO);

}