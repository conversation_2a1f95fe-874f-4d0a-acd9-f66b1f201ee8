package com.smaile.health.mapper;

import com.smaile.health.domain.Organization;
import com.smaile.health.model.OrganizationDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrganizationMapper {

    @Mapping(source = "parent.id", target = "parentId")
    OrganizationDTO toDTO(Organization organization);

    Organization toEntity(OrganizationDTO organizationDTO);

    void updateEntityFromDTO(OrganizationDTO organizationDTO, @MappingTarget Organization organization);
}