package com.smaile.health.mapper;

import com.smaile.health.domain.InsuranceCompany;
import com.smaile.health.model.InsuranceCompanyDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface InsuranceCompanyMapper {

    @Mapping(target = "parent", source = "parent")
    InsuranceCompanyDTO toDTO(InsuranceCompany insuranceCompany);

    InsuranceCompany toEntity(InsuranceCompanyDTO insuranceCompanyDTO);

    @Mapping(target = "status", ignore = true)
    @Mapping(target = "parent", ignore = true)
    void updateEntityFromDTO(InsuranceCompanyDTO insuranceCompanyDTO, @MappingTarget InsuranceCompany insuranceCompany);
}
