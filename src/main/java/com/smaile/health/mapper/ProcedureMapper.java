package com.smaile.health.mapper;

import com.smaile.health.domain.Procedure;
import com.smaile.health.model.ProcedureDTO;
import com.smaile.health.model.DiagnosisSummaryDTO;
import com.smaile.health.model.SpecialitySummaryDTO;
import com.smaile.health.model.ProofSummaryDTO;
import com.smaile.health.model.request.CreateProcedureRequestDTO;
import com.smaile.health.model.request.UpdateProcedureRequestDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

@Mapper(componentModel = "spring", uses = {DiagnosisMapper.class, SpecialityMapper.class, ProofMapper.class})
public abstract class ProcedureMapper extends BaseMapper {

    public abstract ProcedureDTO toDTO(Procedure procedure);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "relatedDiagnoses", ignore = true)
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "dateCreated", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastUpdated", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    public abstract Procedure toEntity(CreateProcedureRequestDTO requestDTO);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "relatedDiagnoses", ignore = true)
    @Mapping(target = "dateCreated", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastUpdated", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "cancellationDate", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "mouthPartRequirements", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    public abstract void updateEntity(UpdateProcedureRequestDTO requestDTO, @MappingTarget Procedure procedure);

    public abstract List<DiagnosisSummaryDTO> toDiagnosisSummaryDTOList(List<com.smaile.health.domain.Diagnosis> diagnoses);

    public abstract List<SpecialitySummaryDTO> toSpecialitySummaryDTOList(List<com.smaile.health.domain.Speciality> specialties);

    public abstract List<ProofSummaryDTO> toProofSummaryDTOList(List<com.smaile.health.domain.Proof> proofs);

}
