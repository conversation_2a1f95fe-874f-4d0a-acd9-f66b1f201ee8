package com.smaile.health.mapper;

import com.smaile.health.domain.Proof;
import com.smaile.health.model.request.CreateProofRequestDTO;
import com.smaile.health.model.ProofDTO;
import com.smaile.health.model.ProofSummaryDTO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class ProofMapper extends BaseMapper {

    public abstract ProofDTO toDTO(Proof proof);

    @AfterMapping
    protected void setStatus(Proof proof, @MappingTarget ProofDTO dto) {
        dto.setStatus(proof.getStatus());
    }


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "isActive", constant = "true")
    public abstract Proof toEntity(CreateProofRequestDTO requestDTO);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "dateCreated", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastUpdated", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    @Mapping(target = "cancellationDate", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    public abstract void updateEntityFromDTO(ProofDTO dto, @MappingTarget Proof proof);

    public abstract ProofSummaryDTO toSummaryDTO(Proof proof);
}
