package com.smaile.health.domain;

import com.smaile.health.endpoint.validation.annotation.ValidDateRange;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "proofs")
@Getter
@Setter
@ValidDateRange
public class Proof extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "code", nullable = false, unique = true)
    private String code;

    @Column(name = "description")
    private String description;

    @Column(name = "effective_date", nullable = false)
    private LocalDate effectiveDate;

    @Column(name = "cancellation_date")
    private LocalDate cancellationDate;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Transient
    private com.smaile.health.constants.Status status;

    /**
     Calculates the current status of the proof based on isActive and cancellationDate

     @return Status - ACTIVE, INACTIVE, or EXPIRED
     */
    public com.smaile.health.constants.Status getStatus() {
        if (status == null) {
            if (!isActive) {
                status = com.smaile.health.constants.Status.INACTIVE;
            } else if (cancellationDate != null && cancellationDate.isBefore(LocalDate.now())) {
                status = com.smaile.health.constants.Status.EXPIRED;
            } else {
                status = com.smaile.health.constants.Status.ACTIVE;
            }
        }
        return status;
    }

    /**
     Sets the status (used for testing or manual override)

     @param status the status to set
     */
    public void setStatus(com.smaile.health.constants.Status status) {
        this.status = status;
    }


    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "diagnosis_proof",
            joinColumns = @JoinColumn(name = "proof_id"),
            inverseJoinColumns = @JoinColumn(name = "diagnosis_id")
    )
    private List<Diagnosis> diagnoses;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "procedure_proof",
            joinColumns = @JoinColumn(name = "proof_id"),
            inverseJoinColumns = @JoinColumn(name = "procedure_id")
    )
    private List<Procedure> procedures;
}
