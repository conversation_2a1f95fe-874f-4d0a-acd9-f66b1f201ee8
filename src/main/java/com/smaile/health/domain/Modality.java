package com.smaile.health.domain;

import com.smaile.health.constants.NetworkType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

@Getter
@Setter
@Entity
@Table(name = "modalities")
public class Modality extends BaseEntity {
    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "ic_org_id", nullable = false)
    private Organization icOrg;

    @Size(max = 255)
    @NotNull
    @Column(name = "code", nullable = false)
    private String code;

    @Size(max = 255)
    @NotNull
    @Column(name = "short_name", nullable = false)
    private String shortName;

    @Size(max = 255)
    @NotNull
    @Column(name = "name", nullable = false)
    private String name;

    @NotNull
    @Column(name = "network_type", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private NetworkType networkType;

    @Column(name = "annual_limit_global", precision = 18, scale = 2)
    private BigDecimal annualLimitGlobal;

    @Column(name = "annual_limit_in", precision = 18, scale = 2)
    private BigDecimal annualLimitIn;

    @Column(name = "annual_limit_out", precision = 18, scale = 2)
    private BigDecimal annualLimitOut;

}