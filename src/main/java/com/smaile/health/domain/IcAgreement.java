package com.smaile.health.domain;

import com.smaile.health.constants.Status;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "ic_agreements")
@Getter
@Setter
public class IcAgreement extends BaseEntity {

    @Id
    @Column(nullable = false, updatable = false)
    private UUID id;

    @Column
    private Instant effectiveTime;

    @Column
    private Instant cancellationTime;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Status status;

    @ManyToOne
    @JoinColumn(name="insurance_company_id", nullable=false)
    private InsuranceCompany insuranceCompany;

    @ManyToOne
    @JoinColumn(name="agreement_id", nullable=false)
    private Agreement agreement;

    public static class Fields {
        public static final String ID = "id";
        public static final String EFFECTIVE_TIME = "effectiveTime";
        public static final String CANCELLATION_TIME = "cancellationTime";
        public static final String STATUS = "status";
        public static final String AGREEMENT = "agreement";
        public static final String IC = "insuranceCompany";
    }
}
