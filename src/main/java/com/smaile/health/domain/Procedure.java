package com.smaile.health.domain;

import com.smaile.health.audit.AuditableEntity;
import com.smaile.health.audit.AuditableField;
import com.smaile.health.constants.AgeGroup;
import com.smaile.health.constants.DiagnosisFrequency;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Entity
@Table(name = "procedure")
@Getter
@Setter
@AuditableEntity(entityName = "Procedure")
public class Procedure extends BaseEntity {

    @Id
    private UUID id;

    @Column(name = "code", nullable = false, unique = true)
    @AuditableField(displayName = "Code")
    private String code;

    @Enumerated(EnumType.STRING)
    @Column(name = "frequency", nullable = false)
    @AuditableField(displayName = "Diagnosis Frequency")
    private DiagnosisFrequency frequency;

    @Column(name = "description", nullable = false, columnDefinition = "TEXT")
    @AuditableField(displayName = "Description")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "age_group", nullable = false)
    @AuditableField(displayName = "Age Group")
    private AgeGroup ageGroup;

    @Column(name = "market_cost", nullable = false, precision = 10, scale = 2)
    @AuditableField(displayName = "Market Cost")
    private BigDecimal marketCost;

    @Column(name = "mouth_part_requirements", columnDefinition = "JSON")
    @Type(JsonType.class)
    @AuditableField(displayName = "Mouth Part Requirements")
    private List<String> mouthPartRequirements;

    @ManyToMany
    @JoinTable(name = "procedure_diagnosis", joinColumns = @JoinColumn(name = "procedure_id"),
            inverseJoinColumns = @JoinColumn(name = "diagnosis_id"))
    private List<Diagnosis> relatedDiagnoses;

    @Column(name = "effective_date", nullable = false)
    @AuditableField(displayName = "Effective Date")
    private LocalDate effectiveDate;

    @Column(name = "cancellation_date")
    @AuditableField(displayName = "Cancellation Date")
    private LocalDate cancellationDate;

    @ManyToMany
    @JoinTable(name = "procedure_speciality", joinColumns = @JoinColumn(name = "procedure_id"),
            inverseJoinColumns = @JoinColumn(name = "speciality_id"))
    private List<Speciality> specialties;

    @ManyToMany
    @JoinTable(name = "procedure_proof", joinColumns = @JoinColumn(name = "procedure_id"),
            inverseJoinColumns = @JoinColumn(name = "proof_id"))
    private List<Proof> proofs;

    @OneToMany(mappedBy = "procedure", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<AgreementProceduresDetail> proceduresDetails;

    @Column(name = "is_active", nullable = false)
    @AuditableField(displayName = "Is Active")
    private Boolean isActive = true;

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof Procedure procedure)) {
            return false;
        }
        return Objects.equals(id, procedure.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

}
