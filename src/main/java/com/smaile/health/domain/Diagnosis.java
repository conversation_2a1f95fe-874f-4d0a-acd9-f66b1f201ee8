package com.smaile.health.domain;

import com.smaile.health.constants.DiagnosisFrequency;
import com.smaile.health.constants.MouthPartType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "diagnosis")
@Getter
@Setter
public class Diagnosis extends BaseEntity {

    @Id
    private UUID id;

    @Column(name = "code", nullable = false, unique = true)
    private String code; // e.g., ICD-10 codes

    @Enumerated(EnumType.STRING)
    @Column(name = "frequency", nullable = false)
    private DiagnosisFrequency frequency;

    @Column(name = "description", nullable = false, columnDefinition = "TEXT")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "mouth_part_type", nullable = false)
    private MouthPartType mouthPartType;

    @Column(name = "mouth_part_id")
    private String mouthPartId;

    @Column(name = "market_cost", nullable = false, precision = 10, scale = 2)
    private BigDecimal marketCost;

    @Column(name = "knowledge_cost", nullable = false, precision = 10, scale = 2)
    private BigDecimal knowledgeCost;

    @Column(name = "effective_date", nullable = false)
    private LocalDate effectiveDate;

    @Column(name = "cancellation_date")
    private LocalDate cancellationDate;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "diagnosis_speciality",
            joinColumns = @JoinColumn(name = "diagnosis_id"),
            inverseJoinColumns = @JoinColumn(name = "speciality_id")
    )
    private List<Speciality> specialties;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "diagnosis_proof",
            joinColumns = @JoinColumn(name = "diagnosis_id"),
            inverseJoinColumns = @JoinColumn(name = "proof_id")
    )
    private List<Proof> proofs;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
}
