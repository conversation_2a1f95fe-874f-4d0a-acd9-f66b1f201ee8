package com.smaile.health.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Entity
@Table(name = "insurance_companies")
@PrimaryKeyJoinColumn(name = "id")
@Getter
@Setter
public class InsuranceCompany extends Organization {

    @Column(name = "market", nullable = false)
    private String market;

    @Column(name = "country", nullable = false)
    private String country;

    @OneToMany(cascade = CascadeType.REMOVE, mappedBy = "insuranceCompany", fetch = FetchType.LAZY)
    private List<IcAgreement> icAgreements;

    public static class Fields {
        public static final String ID = "id";
        public static final String NAME = "name";
    }
}
