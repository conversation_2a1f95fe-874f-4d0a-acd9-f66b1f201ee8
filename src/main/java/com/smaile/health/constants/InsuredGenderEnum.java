package com.smaile.health.constants;

import java.util.HashMap;
import java.util.Map;

public enum InsuredGenderEnum {
    MALE,
    FEMALE,
    OTHER,
    NOT_INFORMED;

    private static final Map<String, InsuredGenderEnum> mapping = new HashMap<>();

    static {
        for(InsuredGenderEnum e : InsuredGenderEnum.values()) {
            mapping.put(e.name(), e);
        }
    }

    public static InsuredGenderEnum resolve(String name) {
        if (name == null) return null;
        return mapping.getOrDefault(name, null);
    }

    public boolean matches(String name) {
        return (this == resolve(name));
    }
}
