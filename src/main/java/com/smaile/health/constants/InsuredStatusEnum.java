package com.smaile.health.constants;

import java.util.HashMap;
import java.util.Map;

public enum InsuredStatusEnum {
    ACTIVE,
    SUSPENDED,
    CANCELLED;

    private static final Map<String, InsuredStatusEnum> map = new HashMap<>();

    static {
        for (InsuredStatusEnum insuredStatusEnum : InsuredStatusEnum.values()) {
            map.put(insuredStatusEnum.name(), insuredStatusEnum);
        }
    }

    public InsuredStatusEnum resolve(String name) {
        if (name == null) return null;
        return map.getOrDefault(name, null);
    }

    public boolean matches(String name) {
        return (this == resolve(name));
    }
}
