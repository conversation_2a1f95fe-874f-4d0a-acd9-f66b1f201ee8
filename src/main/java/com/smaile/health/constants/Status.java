package com.smaile.health.constants;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.Map;

public enum Status {

    ACTIVE,
    INACTIVE,
    EXPIRED,
    DELETED;

    private static final Map<String, Status> mappings = new HashMap<>();
    static {
        for (Status status : Status.values()) {
            mappings.put(status.name(), status);
        }
    }

    @Nullable
    public static Status resolve(@Nullable String status) {
        if (status == null) return null;
        return mappings.getOrDefault(status, null);
    }

    public boolean matches(String status) {
        return (this == resolve(status));
    }


}
