package com.smaile.health.constants;

/**
 * Enum to manage all message keys used throughout the SMAILE project.
 * This provides a centralized way to manage internationalization message keys.
 */
public enum MessageKey {

    FILE_NOT_UPLOADED("msg.file.not-uploaded"),

    // ========================================
    // PROFESSIONAL MESSAGES
    // ========================================
    PROFESSIONAL_LICENSE_ID_EXISTED ("msg.professional.duplicate-license-id"),
    PROFESSIONAL_USERNAME_EXISTED ("msg.professional.duplicate-username"),
    PROFESSIONAL_EMAIL_EXISTED ("msg.professional.duplicate-email"),
    PROFESSIONAL_NOT_FOUND ("msg.professional.id-not-found"),
    PROFESSIONAL_INVALID_STATUS ("msg.professional.invalid-status"),
    PROFESSIONAL_ACTIVATED("msg.professional.activated"),
    PROFESSIONAL_DEACTIVATED("msg.professional.deactivated"),
    PROFESSIONAL_ALREADY_ACTIVE("msg.professional.already-active"),
    PROFESSIONAL_ALREADY_INACTIVE("msg.professional.already-inactive"),

    // ========================================
    // INSURANCE COMPANY MESSAGES
    // ========================================

    // Success messages
    INSURANCE_COMPANY_CREATED("msg.insurance-company.created"),
    INSURANCE_COMPANY_UPDATED("msg.insurance-company.updated"),
    INSURANCE_COMPANY_RETRIEVED("msg.insurance-company.retrieved"),
    INSURANCE_COMPANY_APPROVED("msg.insurance-company.approved"),
    INSURANCE_COMPANY_ACTIVATED("msg.insurance-company.activated"),
    INSURANCE_COMPANY_DEACTIVATED("msg.insurance-company.deactivated"),

    // Error messages
    INSURANCE_COMPANY_OPERATION_FAILED("msg.insurance-company.operation.failed"),
    INSURANCE_COMPANY_CANNOT_UPDATE_INACTIVE("msg.insurance-company.cannot-update-inactive"),
    INSURANCE_COMPANY_ALREADY_INACTIVE("msg.insurance-company.already-inactive"),
    INSURANCE_COMPANY_ALREADY_ACTIVE("msg.insurance-company.already-active"),
    INSURANCE_COMPANY_ALREADY_APPROVED("msg.insurance-company.already-approved"),

    // ========================================
    // TPA MESSAGES
    // ========================================

    // Success messages
    TPA_CREATED("msg.tpa.created"),
    TPA_UPDATED("msg.tpa.updated"),
    TPA_RETRIEVED("msg.tpa.retrieved"),
    TPA_ACTIVATED("msg.tpa.activated"),
    TPA_DEACTIVATED("msg.tpa.deactivated"),

    // Error messages
    TPA_OPERATION_FAILED("msg.tpa.operation.failed"),
    TPA_CANNOT_UPDATE_INACTIVE("msg.tpa.cannot-update-inactive"),
    TPA_ALREADY_INACTIVE("msg.tpa.already-inactive"),
    TPA_ALREADY_ACTIVE("msg.tpa.already-active"),

    // ========================================
    // MEDICAL PROVIDER MESSAGES
    // ========================================

    // Success messages
    MEDICAL_PROVIDER_CREATED("msg.medical-provider.created"),
    MEDICAL_PROVIDER_UPDATED("msg.medical-provider.updated"),
    MEDICAL_PROVIDER_RETRIEVED("msg.medical-provider.retrieved"),
    MEDICAL_PROVIDER_ACTIVATED("msg.medical-provider.activated"),
    MEDICAL_PROVIDER_DEACTIVATED("msg.medical-provider.deactivated"),

    // Error messages
    MEDICAL_PROVIDER_OPERATION_FAILED("msg.medical-provider.operation.failed"),
    MEDICAL_PROVIDER_CANNOT_UPDATE_INACTIVE("msg.medical-provider.cannot-update-inactive"),
    MEDICAL_PROVIDER_ALREADY_INACTIVE("msg.medical-provider.already-inactive"),
    MEDICAL_PROVIDER_ALREADY_ACTIVE("msg.medical-provider.already-active"),

    // ========================================
    // DIAGNOSIS MESSAGES
    // ========================================

    // Success messages
    DIAGNOSIS_CREATED("msg.diagnosis.created"),
    DIAGNOSIS_UPDATED("msg.diagnosis.updated"),
    DIAGNOSIS_RETRIEVED("msg.diagnosis.retrieved"),
    DIAGNOSIS_DEACTIVATED("msg.diagnosis.deactivated"),

    // Error messages
    DIAGNOSIS_OPERATION_FAILED("msg.diagnosis.operation.failed"),

    // ========================================
    // PROCEDURE MESSAGES
    // ========================================

    // Success messages
    PROCEDURE_CREATED("msg.procedure.created"),
    PROCEDURE_UPDATED("msg.procedure.updated"),
    PROCEDURE_RETRIEVED("msg.procedure.retrieved"),
    PROCEDURE_DEACTIVATED("msg.procedure.deactivated"),

    // Error messages
    PROCEDURE_OPERATION_FAILED("msg.procedure.operation.failed"),

    // ========================================
    // SPECIALITY MESSAGES
    // ========================================

    // Success messages
    SPECIALITY_CREATED("msg.speciality.created"),
    SPECIALITY_UPDATED("msg.speciality.updated"),
    SPECIALITY_RETRIEVED("msg.speciality.retrieved"),
    SPECIALITY_DELETED("msg.speciality.deleted"),

    // Error messages
    SPECIALITY_OPERATION_FAILED("msg.speciality.operation.failed"),

    // ========================================
    // MODALITY MESSAGES
    // ========================================
    MODALITY_CREATED("msg.modality.created"),
    MODALITY_UPDATED("msg.modality.updated"),
    MODALITY_RETRIEVED("msg.modality.retrieved"),

    // ========================================
    // ORGANIZATION MESSAGES
    // ========================================

    // Success messages
    ORGANIZATION_CREATED("msg.organization.created"),
    ORGANIZATION_UPDATED("msg.organization.updated"),
    ORGANIZATION_RETRIEVED("msg.organization.retrieved"),
    ORGANIZATION_DELETED("msg.organization.deleted"),

    // ========================================
    // USER MESSAGES
    // ========================================

    // Success messages
    USER_CREATED("msg.user.created"),
    USER_UPDATED("msg.user.updated"),
    USER_RETRIEVED("msg.user.retrieved"),
    USER_DELETED("msg.user.deleted"),

    // Error messages
    USER_OPERATION_FAILED("msg.user.operation.failed"),
    USER_NOT_FOUND("msg.user.not-found"),
    USER_ALREADY_EXISTS("msg.user.already-exists"),
    USER_ACTIVATED("msg.user.activated"),
    USER_DEACTIVATED("msg.user.deactivated"),
    USER_ALREADY_ACTIVE("msg.user.already-active"),
    USER_ALREADY_INACTIVE("msg.user.already-inactive"),

    // ========================================
    // ROLE MESSAGES
    // ========================================

    // Success messages
    ROLE_CREATED("msg.role.created"),
    ROLE_UPDATED("msg.role.updated"),
    ROLE_RETRIEVED("msg.role.retrieved"),
    ROLE_DELETED("msg.role.deleted"),

    // Error messages
    ROLE_OPERATION_FAILED("msg.role.operation.failed"),
    ROLE_NOT_FOUND("msg.role.not-found"),
    ROLE_ALREADY_EXISTS("msg.role.already-exists"),

    // ========================================
    // AUTHENTICATION & AUTHORIZATION MESSAGES
    // ========================================

    // Success messages
    LOGIN_SUCCESS("msg.auth.login.success"),
    LOGOUT_SUCCESS("msg.auth.logout.success"),
    PASSWORD_CHANGED("msg.auth.password.changed"),

    // Error messages
    LOGIN_FAILED("msg.auth.login.failed"),
    INVALID_CREDENTIALS("msg.auth.invalid-credentials"),
    ACCOUNT_LOCKED("msg.auth.account-locked"),
    TOKEN_EXPIRED("msg.auth.token-expired"),
    INSUFFICIENT_PERMISSIONS("msg.auth.insufficient-permissions"),

    // ========================================
    // VALIDATION MESSAGES
    // ========================================

    // Field validation
    FIELD_REQUIRED("msg.validation.field.required"),
    FIELD_INVALID_FORMAT("msg.validation.field.invalid-format"),
    FIELD_TOO_LONG("msg.validation.field.too-long"),
    FIELD_TOO_SHORT("msg.validation.field.too-short"),

    // Business validation
    DUPLICATE_ENTRY("msg.validation.duplicate-entry"),
    REFERENCED_ENTITY("msg.validation.referenced-entity"),
    INVALID_STATUS_TRANSITION("msg.validation.invalid-status-transition"),

    // ========================================
    // SYSTEM MESSAGES
    // ========================================

    // Success messages
    OPERATION_SUCCESS("msg.system.operation.success"),
    DATA_SAVED("msg.system.data.saved"),
    DATA_UPDATED("msg.system.data.updated"),
    DATA_DELETED("msg.system.data.deleted"),

    // Error messages
    OPERATION_FAILED("msg.system.operation.failed"),
    SYSTEM_ERROR("msg.system.error"),
    SERVICE_UNAVAILABLE("msg.system.service-unavailable"),
    DATABASE_ERROR("msg.system.database-error"),

    // ========================================
    // NOTIFICATION MESSAGES
    // ========================================

    // Success notifications
    NOTIFICATION_SENT("msg.notification.sent"),
    EMAIL_SENT("msg.notification.email.sent"),

    // Error notifications
    NOTIFICATION_FAILED("msg.notification.failed"),
    EMAIL_FAILED("msg.notification.email.failed"),

    // ========================================
    // FILE OPERATION MESSAGES
    // ========================================

    // Success messages
    FILE_UPLOADED("msg.file.uploaded"),
    FILE_DOWNLOADED("msg.file.downloaded"),
    FILE_DELETED("msg.file.deleted"),

    // Error messages
    FILE_UPLOAD_FAILED("msg.file.upload.failed"),
    FILE_NOT_FOUND("msg.file.not-found"),
    FILE_TOO_LARGE("msg.file.too-large"),
    INVALID_FILE_TYPE("msg.file.invalid-type"),

    // ========================================
    // SEARCH & FILTER MESSAGES
    // ========================================

    // Success messages
    SEARCH_COMPLETED("msg.search.completed"),
    FILTERS_APPLIED("msg.search.filters-applied"),

    // Information messages
    NO_RESULTS_FOUND("msg.search.no-results"),
    SEARCH_TIMEOUT("msg.search.timeout"),

    // ========================================
    // PAGINATION MESSAGES
    // ========================================

    // Information messages
    PAGE_INFO("msg.pagination.page-info"),
    TOTAL_ELEMENTS("msg.pagination.total-elements"),

    // ========================================
    // PROOF MESSAGES
    // ========================================

    // Success messages
    PROOF_CREATED("msg.proof.created"),
    PROOF_UPDATED("msg.proof.updated"),
    PROOF_RETRIEVED("msg.proof.retrieved"),
    PROOF_DELETED("msg.proof.deleted"),

    // Error messages
    PROOF_OPERATION_FAILED("msg.proof.operation.failed"),
    PROOF_CANNOT_UPDATE_INACTIVE("msg.proof.cannot-update-inactive"),
    PROOF_ALREADY_INACTIVE("msg.proof.already-inactive"),

    // ========================================
    // HEALTH CHECK MESSAGES
    // ========================================

    // Success messages
    HEALTH_CHECK_PASSED("msg.health.check.passed"),
    SERVICE_HEALTHY("msg.health.service.healthy"),

    // Error messages
    HEALTH_CHECK_FAILED("msg.health.check.failed"),
    SERVICE_UNHEALTHY("msg.health.service.unhealthy"),

    // ========================================
    // GENERAL MESSAGES
    // ========================================

    // Success messages
    SUCCESS("msg.general.success"),
    WELCOME("msg.general.welcome"),

    // Information messages
    INFO("msg.general.info"),
    WARNING("msg.general.warning"),

    // Error messages
    ERROR("msg.general.error"),
    UNKNOWN_ERROR("msg.general.unknown-error");

    private final String key;

    MessageKey(String key) {
        this.key = key;
    }

    /**
     * Get the message key string value.
     *
     * @return the message key string
     */
    public String getKey() {
        return key;
    }

    /**
     * Get the message key string value (alias for getKey).
     *
     * @return the message key string
     */
    public String getMessageKey() {
        return key;
    }

    @Override
    public String toString() {
        return key;
    }
}
