package com.smaile.health.constants;

public enum ErrorCode {

    IC_AGREEMENT_NOT_FOUND("error.ic-agreement.id-not-found"),
    IC_AGREEMENT_EXISTING_IC_AND_AGREEMENT_NOT_FOUND("error.ic-agreement.existing-ic-and-agreement"),

    MP_NOT_FOUND("error.agreement.mp-not-found"),
    TIME_RANGE_INVALID("error.agreement.time-range-invalid"),
    CONTRACT_ID_DUPLICATE("error.agreement.contract-id-duplicated"),
    AGREEMENT_NOT_FOUND("error.agreement.not-found"),
    NO_AUTHORIZED_CREATE_AGREEMENT_WITH_MP("error.agreement.not-authorized-with-mp"),

    // Insurance Company errors
    INSURANCE_COMPANY_NOT_FOUND("error.insurance-company.not-found"),
    INSURANCE_COMPANY_NAME_DUPLICATE("error.insurance-company.name-duplicate"),

    // TPA errors
    TPA_NOT_FOUND("error.tpa.not-found"),
    TPA_NAME_DUPLICATE("error.tpa.name-duplicate"),
    INSUFFICIENT_PERMISSIONS_TO_CREATE_TPA("error.tpa.insufficient-permissions-to-create"),

    // Organization errors
    ORGANIZATION_NOT_FOUND("error.organization.not-found"),
    ORGANIZATION_NAME_DUPLICATE("error.organization.name-duplicate"),
    USER_ORGANIZATION_NOT_FOUND("error.user-organization.not-found"),

    // User errors
    USER_NOT_FOUND("error.user.not-found"),
    USER_IS_DELETED("error.user.is-deleted"),
    USER_EMAIL_DUPLICATE("error.user.email-duplicate"),

    // Role errors
    ROLE_NOT_FOUND("error.role.not-found"),

    // Diagnosis errors
    DIAGNOSIS_NOT_FOUND("error.diagnosis.not-found"),
    DIAGNOSIS_CODE_DUPLICATE("error.diagnosis.code-duplicate"),
    DIAGNOSIS_INACTIVE("error.diagnosis.inactive"),
    DIAGNOSIS_ALREADY_INACTIVE("error.diagnosis.already-inactive"),

    // Procedure errors
    PROCEDURE_NOT_FOUND("error.procedure.not-found"),
    PROCEDURE_CODE_DUPLICATE("error.procedure.code-duplicate"),
    PROCEDURE_INACTIVE("error.procedure.inactive"),
    PROCEDURE_ALREADY_INACTIVE("error.procedure.already-inactive"),

    // Specialty errors
    SPECIALITY_NOT_FOUND("error.speciality.not-found"),
    SPECIALITY_INACTIVE("error.speciality.inactive"),
    SPECIALITY_CODE_ALREADY_EXISTS("error.speciality.code-already-exists"),
    SPECIALITY_OPERATION_FAILED("error.speciality.operation-failed"),

    // Modality errors
    MODALITY_CODE_ALREADY_EXISTS("error.modality.code-already-exists"),
    MODALITY_NOT_FOUND("error.modality.not-found"),
    MODALITY_CODE_IS_IMMUTABLE("error.modality.modality-code-is-immutable"),

    // Proof errors
    PROOF_NOT_FOUND("error.proof.not-found"),
    PROOF_INACTIVE("error.proof.inactive"),
    PROOF_CODE_DUPLICATE("error.proof.code-duplicate"),
    PROOF_DATE_RANGE_INVALID("error.proof.date-range-invalid"),
    
    // General errors
    VALIDATION_ERROR("error.validation"),
    INTERNAL_SERVER_ERROR("error.internal-server"),
    PERMISSION_DENIED("error.permission-denied");

    private final String messageKey;

    ErrorCode(String messageKey) {
        this.messageKey = messageKey;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
