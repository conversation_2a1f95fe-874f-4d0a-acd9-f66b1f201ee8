package com.smaile.health.constants;

public enum OrganizationStatus {
    
    ACTIVE("ACTIVE"),
    INACTIVE("INACTIVE"),
    PENDING("PENDING"),
    REJECTED("REJECTED");
    
    private final String value;

    OrganizationStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }
    
    public static OrganizationStatus fromString(String text) {
        for (OrganizationStatus status : OrganizationStatus.values()) {
            if (status.value.equalsIgnoreCase(text)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No constant with text " + text + " found");
    }
    
    @Override
    public String toString() {
        return this.value;
    }
}
