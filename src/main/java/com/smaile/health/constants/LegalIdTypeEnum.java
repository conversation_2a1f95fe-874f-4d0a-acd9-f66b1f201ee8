package com.smaile.health.constants;

import java.util.HashMap;
import java.util.Map;

public enum LegalIdTypeEnum {
    CIF,
    NIF,
    NIE,
    PASSPORT;

    private static final Map<String, LegalIdTypeEnum> mapping = new HashMap<>();

    static {
        for (LegalIdTypeEnum e : LegalIdTypeEnum.values()) {
            mapping.put(e.name(), e);
        }
    }

    public static LegalIdTypeEnum resolve(String name) {
        if (name == null) return null;
        return mapping.getOrDefault(name, null);
    }

    public boolean matches(String name) {
        return (this == resolve(name));
    }
}
