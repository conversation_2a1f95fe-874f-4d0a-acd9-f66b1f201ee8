package com.smaile.health.util;

import java.security.SecureRandom;

public class PasswordUtils {

    private static final String UPPER = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String LOWER = "abcdefghijklmnopqrstuvwxyz";
    private static final String DIGITS = "**********";
    private static final String SPECIAL = "!@#$%^&*";

    private static final SecureRandom RANDOM = new SecureRandom();

    /**
     * Generates a secure temporary password with the specified length
     *
     * @param length the length of the password
     * @return a secure temporary password
     */
    public static String generateTemporaryPassword(int length) {
        if (length < 8) {
            length = 8; // Minimum length for security
        }

        StringBuilder password = new StringBuilder(length);

        // Ensure at least one character from each category
        password.append(UPPER.charAt(RANDOM.nextInt(UPPER.length())));
        password.append(LOWER.charAt(RANDOM.nextInt(LOWER.length())));
        password.append(DIGITS.charAt(RANDOM.nextInt(DIGITS.length())));
        password.append(SPECIAL.charAt(RANDOM.nextInt(SPECIAL.length())));

        // Fill the rest with random characters from all categories
        String allChars = UPPER + LOWER + DIGITS + SPECIAL;
        for (int i = 4; i < length; i++) {
            password.append(allChars.charAt(RANDOM.nextInt(allChars.length())));
        }

        // Shuffle the password to make it more random
        return shuffleString(password.toString());
    }

    /**
     * Generates a default temporary password (12 characters)
     *
     * @return a secure temporary password
     */
    public static String generateTemporaryPassword() {
        return generateTemporaryPassword(12);
    }

    /**
     * Shuffles the characters in a string
     *
     * @param input the input string
     * @return the shuffled string
     */
    private static String shuffleString(String input) {
        char[] characters = input.toCharArray();
        for (int i = characters.length - 1; i > 0; i--) {
            int j = RANDOM.nextInt(i + 1);
            char temp = characters[i];
            characters[i] = characters[j];
            characters[j] = temp;
        }
        return new String(characters);
    }
}
