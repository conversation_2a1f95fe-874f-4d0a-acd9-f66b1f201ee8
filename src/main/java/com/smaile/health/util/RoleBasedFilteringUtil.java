package com.smaile.health.util;

import com.smaile.health.constants.Constants;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.exception.SmaileRuntimeException;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * Enum representing different role combinations for TPA access control
 */
enum RoleCombination {
    IC_ONLY,           // User has only IC-related roles
    SMAILE_ONLY,       // User has only SMAILE-related roles (excluding SUPER_SMAILE)
    BOTH_IC_AND_SMAILE, // User has both IC and SMAILE roles
    NEITHER            // User has no specific IC or SMAILE roles
}

/**
 * Utility class for role-based filtering across different services
 */
@Slf4j
public final class RoleBasedFilteringUtil {

    private RoleBasedFilteringUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Determines the TPA type filter based on the current user's roles
     *
     * @param currentUserRoles the current user's roles
     * @return the TPA type to filter by, or null if no filtering should be applied
     */
    public static OrganizationType determineTPATypeFilterFromRoles(Set<String> currentUserRoles) {
        if (currentUserRoles == null || currentUserRoles.isEmpty()) {
            log.error("User has no roles assigned - this is a security violation");
            throw new SmaileRuntimeException("User must have at least one role assigned to access TPA data");
        }

        // Check if user has SUPER_SMAILE roles (sysadmin privileges)
        if (hasSuperSmaileRole(currentUserRoles)) {
            log.debug("User has SUPER_SMAILE role (sysadmin), showing all TPA types");
            return null; // No filtering - show all types
        }

        // Determine role combination using switch-like logic
        RoleCombination roleCombo = determineRoleCombination(currentUserRoles);

        return switch (roleCombo) {
            case IC_ONLY -> {
                log.debug("User has IC roles, filtering TPAs to show only IC_TPA type");
                yield OrganizationType.IC_TPA;
            }
            case SMAILE_ONLY -> {
                log.debug("User has SMAILE roles, filtering TPAs to show only SMAILE_TPA type");
                yield OrganizationType.SMAILE_TPA;
            }
            case BOTH_IC_AND_SMAILE -> {
                log.debug("User has both IC and SMAILE roles, showing all TPA types");
                yield null; // No filtering - show all types
            }
            case NEITHER -> {
                log.debug("User has no specific IC or SMAILE roles, defaulting to show only IC_TPA type");
                yield OrganizationType.IC_TPA; // Default to IC_TPA for backward compatibility
            }
        };
    }

    /**
     * Checks if the current user has access to the specified TPA based on their roles
     *
     * @param tpaType          the TPA type to check access for
     * @param currentUserRoles the current user's roles
     * @return true if user has access, false otherwise
     */
    public static boolean hasAccessToTPAType(OrganizationType tpaType, Set<String> currentUserRoles) {
        if (currentUserRoles == null || currentUserRoles.isEmpty()) {
            log.error("User has no roles assigned - this is a security violation");
            throw new SmaileRuntimeException("User must have at least one role assigned to access TPA data");
        }

        // Check if user has SUPER_SMAILE roles (sysadmin privileges)
        if (hasSuperSmaileRole(currentUserRoles)) {
            log.debug("User has SUPER_SMAILE role (sysadmin), granting access to all TPA types");
            return true;
        }

        // Determine role combination and check access
        RoleCombination roleCombo = determineRoleCombination(currentUserRoles);

        return switch (roleCombo) {
            case BOTH_IC_AND_SMAILE -> true; // Can access any TPA type
            case IC_ONLY -> tpaType == OrganizationType.IC_TPA; // Can only access IC_TPA
            case SMAILE_ONLY -> tpaType == OrganizationType.SMAILE_TPA; // Can only access SMAILE_TPA
            case NEITHER -> false; // No access to any TPA
        };
    }

    /**
     * Determines if the current user should see all organizations or only those associated with their current organization
     *
     * @param currentUserRoles the current user's roles
     * @return true if user should see all organizations (SUPER_SMAILE_ADMIN), false if filtered by current org
     */
    public static boolean shouldSeeAllOrganizations(Set<String> currentUserRoles) {
        if (currentUserRoles == null || currentUserRoles.isEmpty()) {
            log.error("User has no roles assigned - this is a security violation");
            throw new SmaileRuntimeException("User must have at least one role assigned to access organization data");
        }

        // Check if user has SUPER_SMAILE roles (sysadmin privileges)
        return hasSuperSmaileRole(currentUserRoles);
    }

    /**
     * Checks if the user has SUPER_SMAILE role (sysadmin privileges)
     *
     * @param currentUserRoles the current user's roles
     * @return true if user has SUPER_SMAILE role
     */
    private static boolean hasSuperSmaileRole(Set<String> currentUserRoles) {
        return currentUserRoles.stream()
                .anyMatch(role -> RoleEnum.SUPER_SMAILE_ADMIN.name().equals(role) ||
                        (Constants.ROLE_PREFIX + RoleEnum.SUPER_SMAILE_ADMIN.name()).equals(role));
    }

    /**
     * Determines the role combination for TPA access control
     *
     * @param currentUserRoles the current user's roles
     * @return RoleCombination enum representing the user's role combination
     */
    private static RoleCombination determineRoleCombination(Set<String> currentUserRoles) {
        boolean hasICRole = currentUserRoles.stream()
                .anyMatch(role -> role.startsWith("IC_") || RoleEnum.IC_ADMIN.name().equals(role));

        boolean hasSmaileRole = currentUserRoles.stream()
                .anyMatch(role -> role.startsWith("SMAILE_") && !RoleEnum.SUPER_SMAILE_ADMIN.name().equals(role));

        if (hasICRole && hasSmaileRole) {
            return RoleCombination.BOTH_IC_AND_SMAILE;
        } else if (hasICRole) {
            return RoleCombination.IC_ONLY;
        } else if (hasSmaileRole) {
            return RoleCombination.SMAILE_ONLY;
        } else {
            return RoleCombination.NEITHER;
        }
    }
}
