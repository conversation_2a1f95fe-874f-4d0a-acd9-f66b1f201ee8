package com.smaile.health.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.audit.listener.SmaileAuditDeleteListener;
import com.smaile.health.audit.listener.SmaileAuditInsertListener;
import com.smaile.health.audit.listener.SmaileAuditUpdateListener;
import jakarta.persistence.EntityManagerFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.SessionFactory;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.EventType;
import org.hibernate.internal.SessionFactoryImpl;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

/**
 * Configuration for the audit logging system.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/09/07
 */
@Configuration
@RequiredArgsConstructor
@Slf4j
public class SmaileAuditConfig {

    private final SmaileAuditInsertListener insertListener;
    private final SmaileAuditUpdateListener updateListener;
    private final SmaileAuditDeleteListener deleteListener;

    /**
     * ObjectMapper bean for JSON serialization in audit entries.
     */
    @Bean
    public ObjectMapper auditObjectMapper() {
        return new ObjectMapper();
    }

    /**
     * Register the audit listeners.
     */
    @EventListener
    public void onApplicationReady(ApplicationReadyEvent event) {
        try {
            SessionFactory sessionFactory = getSessionFactory(event.getApplicationContext());

            if (sessionFactory == null) {
                log.error("Could not obtain SessionFactory for audit listener registration");
                return;
            }

            SessionFactoryImpl sessionFactoryImpl = unwrapSessionFactory(sessionFactory);

            if (sessionFactoryImpl == null) {
                log.error("Could not unwrap SessionFactory to SessionFactoryImpl");
                return;
            }

            EventListenerRegistry registry = sessionFactoryImpl.getServiceRegistry()
                    .getService(EventListenerRegistry.class);

            registry.getEventListenerGroup(EventType.POST_INSERT).appendListener(insertListener);
            registry.getEventListenerGroup(EventType.POST_UPDATE).appendListener(updateListener);
            registry.getEventListenerGroup(EventType.POST_DELETE).appendListener(deleteListener);

            log.info("Simple audit listeners registered successfully");
        } catch (Exception e) {
            log.error("Failed to register audit listeners: {}", e.getMessage(), e);
            throw new RuntimeException("Could not register audit listeners", e);
        }
    }

    private SessionFactory getSessionFactory(ApplicationContext applicationContext) {
        try {
            return applicationContext.getBean(SessionFactory.class);
        } catch (Exception e) {
            log.debug("Direct SessionFactory bean not found, trying through EntityManagerFactory");
            try {
                EntityManagerFactory entityManagerFactory = applicationContext.getBean(EntityManagerFactory.class);
                return entityManagerFactory.unwrap(SessionFactory.class);
            } catch (Exception ex) {
                log.error("Could not obtain SessionFactory: {}", ex.getMessage());
                return null;
            }
        }
    }

    private SessionFactoryImpl unwrapSessionFactory(SessionFactory sessionFactory) {
        try {
            if (sessionFactory instanceof SessionFactoryImpl sfImpl) {
                return sfImpl;
            } else {
                return sessionFactory.unwrap(SessionFactoryImpl.class);
            }
        } catch (Exception e) {
            log.error("Could not unwrap SessionFactory: {}", e.getMessage());
            return null;
        }
    }

}
