package com.smaile.health.config;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.smaile.health.audit.EntityAuditMetadata;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * Configuration for caching components used throughout the application.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/09/12
 */
@Configuration
public class CacheConfig {

    /**
     * Creates a Guava cache for storing entity audit metadata.
     * This cache is used by CachedAuditFieldProcessor to cache reflection-based
     * metadata about entity classes to improve performance.
     *
     * @return Cache instance for EntityAuditMetadata
     */
    @Bean
    public Cache<Class<?>, EntityAuditMetadata> entityAuditMetadataCache() {
        return CacheBuilder.newBuilder()
                .maximumSize(1000)  // Maximum number of entries
                .expireAfterWrite(1, TimeUnit.MINUTES)
                .expireAfterAccess(10, TimeUnit.MINUTES)
                .recordStats()  // Enable statistics for monitoring
                .build();
    }
}
