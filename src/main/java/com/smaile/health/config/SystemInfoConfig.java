package com.smaile.health.config;

import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Currency;
import java.util.List;
import java.util.Locale;
import java.util.Set;

@Component
@ConfigurationProperties(prefix = "country")
@Getter
@Setter
public class SystemInfoConfig {
    private String countryCode;
    private String countryDescription;
    private String currencyCode;
    private String currencyDescription;
    private String defaultLanguageCode;
    private String defaultLanguageDescription;
    private List<Language> additionalLanguages;

    @Getter
    @Setter
    public static class Language {
        private String code;
        private String description;
    }


    @PostConstruct
    void validateIsoCodes() {
        if (!Set.of(Locale.getISOCountries()).contains(countryCode)) {
            throw new IllegalArgumentException("Invalid country code: " + countryCode);
        }

        try {
            Currency.getInstance(currencyCode);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid currency code: " + currencyCode);
        }

        if (!Set.of(Locale.getISOLanguages()).contains(defaultLanguageCode)) {
            throw new IllegalArgumentException("Invalid default language code: " + defaultLanguageCode);
        }

        for (Language lang : additionalLanguages) {
            if (!Set.of(Locale.getISOLanguages()).contains(lang.getCode())) {
                throw new IllegalArgumentException("Invalid additional language code: " + lang.getCode());
            }
        }
    }
}