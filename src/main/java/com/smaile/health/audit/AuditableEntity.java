package com.smaile.health.audit;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation for marking entities as auditable.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/09/07
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AuditableEntity {

    String entityName() default "";

    boolean enabled() default true;

}