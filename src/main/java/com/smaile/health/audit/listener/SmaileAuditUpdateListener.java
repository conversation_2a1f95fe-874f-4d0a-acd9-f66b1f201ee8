package com.smaile.health.audit.listener;

import com.smaile.health.audit.AuditContext;
import com.smaile.health.audit.AuditLogEntry;
import com.smaile.health.audit.AuditLogWriter;
import com.smaile.health.audit.builder.AuditLogEntryBuilder;
import com.smaile.health.config.AppConfig;
import com.smaile.health.domain.AuditLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.event.spi.PostUpdateEvent;
import org.hibernate.event.spi.PostUpdateEventListener;
import org.hibernate.persister.entity.EntityPersister;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Audit listener for UPDATE operations.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/09/07
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SmaileAuditUpdateListener implements PostUpdateEventListener {

    private final AuditLogWriter auditLogWriter;
    private final AuditLogEntryBuilder auditLogEntryBuilder;
    private final AppConfig appConfig;

    @Override
    public void onPostUpdate(PostUpdateEvent event) {
        if (!appConfig.getAudit().isEnabled() || AuditContext.isAuditInProgress()) {
            return;
        }

        Object entity = event.getEntity();

        if (entity instanceof AuditLog) {
            return;
        }

        try {
            String entityId = String.valueOf(event.getId());

            EntityPersister persister = event.getPersister();
            String[] propertyNames = persister.getPropertyNames();
            Object[] currentState = event.getState();
            Object[] previousState = event.getOldState();
            int[] dirtyProperties = event.getDirtyProperties();

            Map<String, Object> oldValues = new HashMap<>();
            Map<String, Object> newValues = new HashMap<>();
            List<String> changedFields = new ArrayList<>();

            for (int dirtyIndex : dirtyProperties) {
                String propertyName = propertyNames[dirtyIndex];
                oldValues.put(propertyName, previousState[dirtyIndex]);
                newValues.put(propertyName, currentState[dirtyIndex]);
                changedFields.add(propertyName);
            }

            AuditLogEntry entry = auditLogEntryBuilder.buildUpdateEntry(entity, entityId, oldValues, newValues,
                    changedFields);

            if (entry != null) {
                auditLogWriter.writeAuditLog(entry);
            }
        } catch (Exception e) {
            log.error("Error processing update audit for entity {}: {}", entity.getClass().getSimpleName(),
                    e.getMessage(), e);
        }
    }

    @Override
    public boolean requiresPostCommitHandling(EntityPersister persister) {
        return false;
    }

}
