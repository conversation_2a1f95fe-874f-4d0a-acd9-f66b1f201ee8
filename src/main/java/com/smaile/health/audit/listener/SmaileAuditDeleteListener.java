package com.smaile.health.audit.listener;

import com.smaile.health.audit.AuditContext;
import com.smaile.health.audit.AuditLogEntry;
import com.smaile.health.audit.AuditLogWriter;
import com.smaile.health.audit.builder.AuditLogEntryBuilder;
import com.smaile.health.config.AppConfig;
import com.smaile.health.domain.AuditLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.event.spi.PostDeleteEvent;
import org.hibernate.event.spi.PostDeleteEventListener;
import org.hibernate.persister.entity.EntityPersister;
import org.springframework.stereotype.Component;

/**
 * Audit listener for DELETE operations.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/09/07
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SmaileAuditDeleteListener implements PostDeleteEventListener {

    private final AuditLogWriter auditLogWriter;
    private final AuditLogEntryBuilder auditLogEntryBuilder;
    private final AppConfig appConfig;

    @Override
    public void onPostDelete(PostDeleteEvent event) {
        if (!appConfig.getAudit().isEnabled() || AuditContext.isAuditInProgress()) {
            return;
        }

        Object entity = event.getEntity();

        if (entity instanceof AuditLog) {
            return;
        }

        try {
            AuditLogEntry entry = auditLogEntryBuilder.buildDeleteEntry(entity);
            if (entry != null) {
                auditLogWriter.writeAuditLog(entry);
            }
        } catch (Exception e) {
            log.error("Error processing delete audit for entity {}: {}", entity.getClass().getSimpleName(),
                    e.getMessage(), e);
        }
    }

    @Override
    public boolean requiresPostCommitHandling(EntityPersister persister) {
        return false;
    }

}
