package com.smaile.health.audit.builder;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.audit.AuditContext;
import com.smaile.health.audit.AuditLogEntry;
import com.smaile.health.audit.CachedAuditFieldProcessor;
import com.smaile.health.audit.EntityAuditMetadata;
import com.smaile.health.audit.FieldAuditMetadata;
import com.smaile.health.constants.AuditOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Builder for creating AuditLogEntry objects from entity changes.
 * Consolidates field-level changes into JSON format for efficient storage.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/09/07
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AuditLogEntryBuilder {

    private final CachedAuditFieldProcessor fieldProcessor;
    private final ObjectMapper objectMapper;

    /**
     * Build audit entry for INSERT operation.
     */
    public AuditLogEntry buildInsertEntry(Object entity) {
        EntityAuditMetadata metadata = fieldProcessor.getEntityMetadata(entity.getClass());

        if (!metadata.isAuditEnabled() || metadata.getAuditableFields().isEmpty()) {
            return null;
        }

        String entityName = metadata.getEntityName();
        String entityId = fieldProcessor.getEntityId(entity, metadata);

        Map<String, Object> newValues = new HashMap<>();
        List<String> changedColumns = new ArrayList<>();

        for (FieldAuditMetadata fieldMetadata : metadata.getAuditableFields()) {
            if (!fieldMetadata.isAuditInsert()) {
                continue;
            }

            try {
                Object value = fieldMetadata.getValue(entity);
                if (value == null && !fieldMetadata.isAuditNullValues()) {
                    continue;
                }

                String formattedValue = fieldProcessor.formatValue(value);
                newValues.put(fieldMetadata.getFieldName(), formattedValue);
                changedColumns.add(fieldMetadata.getFieldName());

            } catch (IllegalAccessException e) {
                log.debug("Cannot access field {} for audit: {}", fieldMetadata.getFieldName(), e.getMessage());
            }
        }

        if (changedColumns.isEmpty()) {
            return null;
        }

        AuditContext.AuditUserInfo userInfo = AuditContext.getCurrentUserInfo();

        return AuditLogEntry.builder()
                .entityName(entityName)
                .entityId(entityId)
                .operation(AuditOperation.INSERT)
                .oldValuesJson(null)
                .newValuesJson(toJson(newValues))
                .changedColumns(changedColumns)
                .createdBy(userInfo.getUsername())
                .createdAt(userInfo.getTimestamp())
                .build();
    }

    /**
     * Build audit entry for UPDATE operation.
     */
    public AuditLogEntry buildUpdateEntry(Object entity, String entityId,
                                          Map<String, Object> oldValues,
                                          Map<String, Object> newValues,
                                          List<String> changedFields) {
        EntityAuditMetadata metadata = fieldProcessor.getEntityMetadata(entity.getClass());

        if (!metadata.isAuditEnabled() || metadata.getAuditableFields().isEmpty()) {
            return null;
        }

        Map<String, Object> auditOldValues = new HashMap<>();
        Map<String, Object> auditNewValues = new HashMap<>();
        List<String> auditChangedColumns = new ArrayList<>();

        for (FieldAuditMetadata fieldMetadata : metadata.getAuditableFields()) {
            if (!fieldMetadata.isAuditUpdate()) {
                continue;
            }

            String fieldName = fieldMetadata.getFieldName();
            if (!changedFields.contains(fieldName)) {
                continue;
            }

            Object oldValue = oldValues.get(fieldName);
            Object newValue = newValues.get(fieldName);

            if (newValue == null && !fieldMetadata.isAuditNullValues()) {
                continue;
            }

            String formattedOldValue = fieldProcessor.formatValue(oldValue);
            String formattedNewValue = fieldProcessor.formatValue(newValue);

            auditOldValues.put(fieldName, formattedOldValue);
            auditNewValues.put(fieldName, formattedNewValue);
            auditChangedColumns.add(fieldName);
        }

        if (auditChangedColumns.isEmpty()) {
            return null;
        }

        AuditContext.AuditUserInfo userInfo = AuditContext.getCurrentUserInfo();

        return AuditLogEntry.builder()
                .entityName(metadata.getEntityName())
                .entityId(entityId)
                .operation(AuditOperation.UPDATE)
                .oldValuesJson(toJson(auditOldValues))
                .newValuesJson(toJson(auditNewValues))
                .changedColumns(auditChangedColumns)
                .createdBy(userInfo.getUsername())
                .createdAt(userInfo.getTimestamp())
                .build();
    }

    /**
     * Build audit entry for DELETE operation.
     */
    public AuditLogEntry buildDeleteEntry(Object entity) {
        EntityAuditMetadata metadata = fieldProcessor.getEntityMetadata(entity.getClass());

        if (!metadata.isAuditEnabled()) {
            return null;
        }

        Map<String, Object> oldValues = new HashMap<>();
        List<String> changedColumns = new ArrayList<>();

        for (FieldAuditMetadata fieldMetadata : metadata.getAuditableFields()) {
            if (!fieldMetadata.isAuditDelete()) {
                continue;
            }

            try {
                Object value = fieldMetadata.getValue(entity);
                if (value == null && !fieldMetadata.isAuditNullValues()) {
                    continue;
                }

                String formattedValue = fieldProcessor.formatValue(value);
                oldValues.put(fieldMetadata.getFieldName(), formattedValue);
                changedColumns.add(fieldMetadata.getFieldName());

            } catch (IllegalAccessException e) {
                log.debug("Cannot access field {} for audit: {}", fieldMetadata.getFieldName(), e.getMessage());
            }
        }

        if (changedColumns.isEmpty()) {
            return null;
        }

        String entityName = metadata.getEntityName();
        String entityId = fieldProcessor.getEntityId(entity, metadata);

        AuditContext.AuditUserInfo userInfo = AuditContext.getCurrentUserInfo();

        return AuditLogEntry.builder()
                .entityName(entityName)
                .entityId(entityId)
                .operation(AuditOperation.DELETE)
                .oldValuesJson(toJson(oldValues))
                .newValuesJson(null)
                .changedColumns(new ArrayList<>())
                .createdBy(userInfo.getUsername())
                .createdAt(userInfo.getTimestamp())
                .build();
    }

    private String toJson(Map<String, Object> values) {
        if (values == null || values.isEmpty()) {
            return null;
        }

        try {
            return objectMapper.writeValueAsString(values);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize audit values to JSON: {}", e.getMessage(), e);
            return "{}";
        }
    }

}
