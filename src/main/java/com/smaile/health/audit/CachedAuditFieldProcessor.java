package com.smaile.health.audit;

import com.google.common.cache.Cache;
import com.smaile.health.config.AppConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * Processes audit metadata for entities and fields with caching.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/09/07
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CachedAuditFieldProcessor {

    private final Cache<Class<?>, EntityAuditMetadata> metadataCache;
    private final AppConfig appConfig;

    public EntityAuditMetadata getEntityMetadata(Class<?> entityClass) {
        if (!appConfig.getAudit().isCacheEnabled()) {
            return new EntityAuditMetadata(entityClass);
        }

        try {
            return metadataCache.get(entityClass, () -> new EntityAuditMetadata(entityClass));
        } catch (Exception e) {
            log.warn("Failed to get cached metadata for {}, creating new instance: {}",
                    entityClass.getSimpleName(), e.getMessage());
            return new EntityAuditMetadata(entityClass);
        }
    }

    public String getEntityId(Object entity, EntityAuditMetadata metadata) {
        if (metadata.getIdField() == null) {
            return "UNKNOWN";
        }

        try {
            Object id = metadata.getIdField().get(entity);
            return id != null ? String.valueOf(id) : "UNKNOWN";
        } catch (IllegalAccessException e) {
            return "UNKNOWN";
        }
    }

    public String formatValue(Object value) {
        if (value == null) {
            return null;
        }
        return convertToString(value);
    }

    private String convertToString(Object value) {
        if (value instanceof Date date) {
            return String.valueOf((date).getTime());
        } else if (value instanceof LocalDateTime ldt) {
            return (ldt).toString();
        } else if (value instanceof BigDecimal bd) {
            return (bd).toPlainString();
        }
        return String.valueOf(value);
    }

}