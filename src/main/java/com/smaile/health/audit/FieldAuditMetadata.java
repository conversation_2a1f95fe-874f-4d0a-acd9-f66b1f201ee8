package com.smaile.health.audit;

import lombok.Data;

import java.lang.reflect.Field;

/**
 * Metadata for an auditable field within an entity.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/09/07
 */
@Data
public class FieldAuditMetadata {

    private final Field field;
    private final String fieldName;
    private final String displayName;
    private final AuditableField annotation;
    private final boolean auditInsert;
    private final boolean auditUpdate;
    private final boolean auditDelete;
    private final boolean auditNullValues;

    public FieldAuditMetadata(Field field, AuditableField annotation) {
        this.field = field;
        this.fieldName = field.getName();
        this.annotation = annotation;
        this.displayName = annotation.displayName().isEmpty() ? fieldName : annotation.displayName();
        this.auditInsert = annotation.auditInsert();
        this.auditUpdate = annotation.auditUpdate();
        this.auditDelete = annotation.auditDelete();
        this.auditNullValues = annotation.auditNullValues();
    }

    public Object getValue(Object entity) throws IllegalAccessException {
        return field.get(entity);
    }

}