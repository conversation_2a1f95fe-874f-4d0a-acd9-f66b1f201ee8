package com.smaile.health.audit;

import com.smaile.health.constants.AuditOperation;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Data Transfer Object for audit log entries.
 * Contains all information needed to create an audit log record.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/09/07
 */
@Data
@Builder
public class AuditLogEntry {

    private final String entityName;
    private final String entityId;
    private final AuditOperation operation;
    private final String oldValuesJson;
    private final String newValuesJson;
    private final List<String> changedColumns;
    private final String createdBy;
    private final LocalDateTime createdAt;

}
