package com.smaile.health.audit;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation for marking fields as auditable.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/09/07
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AuditableField {

    String displayName() default "";

    boolean auditInsert() default true;

    boolean auditUpdate() default true;

    boolean auditDelete() default true;

    boolean auditNullValues() default false;

}