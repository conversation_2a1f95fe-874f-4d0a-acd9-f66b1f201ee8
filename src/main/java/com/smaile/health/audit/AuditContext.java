package com.smaile.health.audit;

import com.smaile.health.util.SecurityUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * Thread-local context for audit operations and provide current user information for audit entries.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/09/07
 */
@Slf4j
public final class AuditContext {

    private static final ThreadLocal<Boolean> AUDIT_IN_PROGRESS = new ThreadLocal<>();

    private AuditContext() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Check if audit is currently in progress on this thread to prevent circular auditing.
     *
     * @return true if audit is in progress, false otherwise
     */
    public static boolean isAuditInProgress() {
        return Boolean.TRUE.equals(AUDIT_IN_PROGRESS.get());
    }

    /**
     * Set audit in progress flag for this thread.
     *
     * @param inProgress true to mark audit as in progress, false to clear
     */
    public static void setAuditInProgress(boolean inProgress) {
        if (inProgress) {
            AUDIT_IN_PROGRESS.set(true);
        } else {
            AUDIT_IN_PROGRESS.remove();
        }
    }

    /**
     * Get current user information for audit entries.
     *
     * @return AuditUserInfo containing username and timestamp
     */
    public static AuditUserInfo getCurrentUserInfo() {
        String username;
        try {
            username = SecurityUtils.getActorContext().getActor().getId().toString();
        } catch (Exception e) {
            log.debug("Could not get current user from security context: {}", e.getMessage());
            username = "system";
        }

        return new AuditUserInfo(username, LocalDateTime.now());
    }

    /**
     * Data class to hold audit user information.
     */
    @Data
    public static class AuditUserInfo {

        private final String username;
        private final LocalDateTime timestamp;

    }

}
