package com.smaile.health.audit;

import jakarta.persistence.Id;
import lombok.Data;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * Metadata for an auditable entity, including its fields and configuration.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/09/07
 */
@Data
public class EntityAuditMetadata {

    private final String entityName;
    private final List<FieldAuditMetadata> auditableFields;
    private final Field idField;
    private final boolean auditEnabled;

    public EntityAuditMetadata(Class<?> entityClass) {
        AuditableEntity entityAnnotation = entityClass.getAnnotation(AuditableEntity.class);

        this.auditEnabled = entityAnnotation == null || entityAnnotation.enabled();
        this.entityName = entityAnnotation != null && !entityAnnotation.entityName().isEmpty()
                ? entityAnnotation.entityName()
                : entityClass.getSimpleName();
        this.idField = findIdField(entityClass);
        this.auditableFields = findAuditableFields(entityClass);
    }

    private Field findIdField(Class<?> entityClass) {
        Class<?> currentClass = entityClass;
        while (currentClass != null && currentClass != Object.class) {
            for (Field field : currentClass.getDeclaredFields()) {
                if (field.isAnnotationPresent(Id.class)) {
                    field.setAccessible(true);
                    return field;
                }
            }
            currentClass = currentClass.getSuperclass();
        }
        return null;
    }

    private List<FieldAuditMetadata> findAuditableFields(Class<?> entityClass) {
        List<FieldAuditMetadata> fields = new ArrayList<>();
        Class<?> currentClass = entityClass;

        while (currentClass != null && currentClass != Object.class) {
            for (Field field : currentClass.getDeclaredFields()) {
                AuditableField annotation = field.getAnnotation(AuditableField.class);
                if (annotation != null) {
                    field.setAccessible(true);
                    fields.add(new FieldAuditMetadata(field, annotation));
                }
            }
            currentClass = currentClass.getSuperclass();
        }

        return fields;
    }

}