package com.smaile.health.service;

import com.smaile.health.constants.Status;
import com.smaile.health.domain.Proof;
import com.smaile.health.model.ProofDTO;
import com.smaile.health.model.ProofSummaryDTO;
import com.smaile.health.model.ProofSummaryResponseDTO;
import com.smaile.health.model.request.CreateProofRequestDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.List;
import java.util.UUID;

public interface ProofService {

    /**
     * Validates and returns all provided proof IDs that exist and are active
     * @param proofIds List of proof IDs to validate and retrieve
     * @return List of Proof entities
     * @throws com.smaile.health.exception.ValidationException if any ID is not found or inactive
     */
    List<Proof> getValidProofs(List<UUID> proofIds);

    /**
     Search proofs with pagination and filters

     @param filter   Filter - searches in code or description fields (case-insensitive)
     @param pageable Pagination parameters
     @return Page of ProofDTO
     */
//    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    Page<ProofDTO> search(String filter, Pageable pageable);

    /**
     Search proofs with pagination and filters including status

     @param filter   Filter - searches in code or description fields (case-insensitive)
     @param status   Status filter - ACTIVE, INACTIVE, or EXPIRED
     @param pageable Pagination parameters
     @return Page of ProofDTO
     */
//    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    Page<ProofDTO> search(String filter, Status status, Pageable pageable);

    /**
     Get proof by ID

     @param id Proof ID
     @return ProofDTO
     @throws com.smaile.health.exception.NotFoundException if proof not found
     */
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    ProofDTO get(UUID id);

    /**
     Create a new proof

     @param requestDTO Create proof request
     @return Created proof ID
     @throws com.smaile.health.exception.ValidationException if validation fails
     */
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    UUID create(CreateProofRequestDTO requestDTO);

    /**
     Update an existing proof

     @param id       Proof ID
     @param proofDTO Proof data to update
     @throws com.smaile.health.exception.NotFoundException   if proof not found
     @throws com.smaile.health.exception.ValidationException if validation fails
     */
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    void update(UUID id, ProofDTO proofDTO);

    /**
     Delete (deactivate) a proof

     @param id Proof ID
     @throws com.smaile.health.exception.NotFoundException if proof not found
     */
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    void delete(UUID id);

    /**
     Get all active proofs as summary DTOs

     @return List of ProofSummaryDTO
     */
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    List<ProofSummaryDTO> getActiveProofs();

    /**
     Get proof summary statistics including total count and count by status

     @return ProofSummaryResponseDTO with counts for each status
     */
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    ProofSummaryResponseDTO getSummary();
}
