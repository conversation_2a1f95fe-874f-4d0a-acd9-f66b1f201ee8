package com.smaile.health.service;

import com.smaile.health.model.AgreementDTO;
import com.smaile.health.model.request.CreateAgreementDTO;
import com.smaile.health.model.request.Filter;
import com.smaile.health.model.request.UpdateAgreementDTO;
import com.smaile.health.model.request.UpdateAgreementProcedureRequest;
import com.smaile.health.util.PageResponse;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface AgreementService {

    PageResponse<AgreementDTO> query(String search, List<Filter> filters, Pageable pageable);

    AgreementDTO detail(UUID agreementId);

    UUID create(CreateAgreementDTO agreement);

    void update(UUID id, UpdateAgreementDTO agreement);

    void updateProcedure(UUID id, UpdateAgreementProcedureRequest agreementProcedureDTO);

}
