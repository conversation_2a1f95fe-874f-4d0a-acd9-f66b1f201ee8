package com.smaile.health.service;

import com.smaile.health.model.ProcedureDTO;
import com.smaile.health.model.request.CreateProcedureRequestDTO;
import com.smaile.health.model.request.UpdateProcedureRequestDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface ProcedureService {

    ProcedureDTO createProcedure(CreateProcedureRequestDTO requestDTO);

    ProcedureDTO updateProcedure(UUID id, UpdateProcedureRequestDTO requestDTO);

    ProcedureDTO getProcedureById(UUID id);

    Page<ProcedureDTO> search(String filter, Pageable pageable);

    void deleteProcedure(UUID id);
}
