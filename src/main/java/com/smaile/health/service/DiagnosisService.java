package com.smaile.health.service;

import com.smaile.health.model.request.CreateDiagnosisRequestDTO;
import com.smaile.health.model.DiagnosisDTO;
import com.smaile.health.model.DiagnosisSummaryDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface DiagnosisService {

    Page<DiagnosisDTO> search(String filter, Pageable pageable);

    DiagnosisDTO get(UUID id);

    UUID create(CreateDiagnosisRequestDTO requestDTO);

    void update(UUID id, DiagnosisDTO diagnosisDTO);

    void delete(UUID id);

    List<DiagnosisSummaryDTO> getAllSummaries();
}
