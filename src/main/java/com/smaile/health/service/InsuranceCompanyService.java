package com.smaile.health.service;

import com.smaile.health.model.request.CreateInsuranceCompanyRequestDTO;
import com.smaile.health.model.InsuranceCompanyDTO;
import com.smaile.health.model.InsuranceCompanyGeneralInfoDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface InsuranceCompanyService {

    Page<InsuranceCompanyDTO> search(String market, String status, String name, Pageable pageable);

    InsuranceCompanyDTO get(UUID id);

    UUID create(CreateInsuranceCompanyRequestDTO requestDTO);

    void update(UUID id, InsuranceCompanyDTO insuranceCompanyDTO);

    void activate(UUID id);

    void deactivate(UUID id);

    void delete(UUID id);

    List<InsuranceCompanyGeneralInfoDTO> getActiveInsuranceCompanies();
}
