package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.domain.Modality;
import com.smaile.health.domain.Organization;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.ModalityMapper;
import com.smaile.health.mapper.OrganizationMapper;
import com.smaile.health.model.ModalityDTO;
import com.smaile.health.model.ModalitySearchCriteria;
import com.smaile.health.model.request.CreateModalityRequestDTO;
import com.smaile.health.model.request.UpdateModalityRequestDTO;
import com.smaile.health.model.response.DetailModalityResponse;
import com.smaile.health.repository.ModalityRepository;
import com.smaile.health.repository.specification.ModalitySpecification;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ModalityService;
import com.smaile.health.util.SecurityUtils;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class ModalityServiceImpl implements ModalityService {

    private final ModalityRepository modalityRepository;

    private final I18nService i18nService;

    private final ModalityMapper modalityMapper;

    private final OrganizationMapper organizationMapper;

    @Override
    @LogExecution
    @PreAuthorize("hasRole('IC_ADMIN')")
    @Transactional
    public UUID create(CreateModalityRequestDTO createModalityRequestDTO) {

        Optional<Modality> checkModality = modalityRepository.findByCode(createModalityRequestDTO.getCode());
        if (checkModality.isPresent()) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.MODALITY_CODE_ALREADY_EXISTS.getMessageKey())
            );
        }

        Organization organization = SecurityUtils.getActorOrganization()
                .orElseThrow(() -> new SmaileRuntimeException("Cannot find organization for actor"));

        if (organization.getType() != OrganizationType.IC) {
            throw new SmaileRuntimeException("Organization type is not IC");
        }

        Modality modality = modalityMapper.dtoToEntity(createModalityRequestDTO);
        modality.setIcOrg(organization);

        Modality insertedModality = modalityRepository.save(modality);

        return insertedModality.getId();
    }

    @Override
    @LogExecution
    @PreAuthorize("hasRole('IC_ADMIN') and @modalityOwnership.owns(#modalityId)")
    @Transactional
    public void update(UUID modalityId, UpdateModalityRequestDTO updateModalityRequestDTO) {
        Modality modality = modalityRepository.findById(modalityId).orElseThrow(
                () -> new ValidationException(
                        i18nService.getMessage(ErrorCode.MODALITY_NOT_FOUND.getMessageKey())
                )
        );

        if (! updateModalityRequestDTO.getCode().equals(modality.getCode())) {
            throw  new ValidationException(
                    i18nService.getMessage(ErrorCode.MODALITY_CODE_IS_IMMUTABLE.getMessageKey())
            );
        }

        modality.setName(updateModalityRequestDTO.getName());
        modality.setShortName(updateModalityRequestDTO.getShortName());
        modality.setNetworkType(updateModalityRequestDTO.getNetworkType());

        modality.setAnnualLimitGlobal(updateModalityRequestDTO.getAnnualLimitGlobal());
        modality.setAnnualLimitIn(updateModalityRequestDTO.getAnnualLimitIn());
        modality.setAnnualLimitOut(updateModalityRequestDTO.getAnnualLimitOut());

        modalityRepository.save(modality);
    }

    @Override
    @LogExecution
    @Transactional
    @PreAuthorize("hasRole('IC_ADMIN') and @modalityOwnership.owns(#modalityId)")
    public DetailModalityResponse detail(UUID modalityId) {
        Modality modality = modalityRepository.findById(modalityId).orElseThrow(
                () -> new ValidationException(
                        i18nService.getMessage(ErrorCode.MODALITY_NOT_FOUND.getMessageKey())
                )
        );

        DetailModalityResponse responseDTO = modalityMapper.entityToResponse(modality);
        responseDTO.setOrganizationInfo(
                organizationMapper.toDTO(modality.getIcOrg())
        );

        return responseDTO;
    }

    @Override
    @PreAuthorize("hasRole('IC_ADMIN')")
    public Page<ModalityDTO> query(ModalitySearchCriteria criteria, Pageable pageable) {
        Organization organization = SecurityUtils.getActorOrganization()
                .orElseThrow(() -> new SmaileRuntimeException("Cannot find organization for actor"));

        Specification<Modality> specification = ModalitySpecification.search(criteria, organization.getId());

        return modalityRepository.findAll(specification, pageable).map(
                modalityMapper::entityToDto
        );
    }

}
