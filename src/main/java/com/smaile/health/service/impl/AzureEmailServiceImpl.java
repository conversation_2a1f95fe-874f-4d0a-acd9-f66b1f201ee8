package com.smaile.health.service.impl;

import com.azure.communication.email.EmailClient;
import com.azure.communication.email.models.EmailAddress;
import com.azure.communication.email.models.EmailAttachment;
import com.azure.communication.email.models.EmailMessage;
import com.azure.communication.email.models.EmailSendResult;
import com.azure.communication.email.models.EmailSendStatus;
import com.azure.core.util.BinaryData;
import com.azure.core.util.polling.SyncPoller;
import com.smaile.health.aop.LogExecution;
import com.smaile.health.config.AppConfig;
import com.smaile.health.config.EmailConfig;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.domain.Organization;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.model.EmailRequest;
import com.smaile.health.model.EmailResponse;
import com.smaile.health.model.SmaileUserCredential;
import com.smaile.health.model.UserDTO;
import com.smaile.health.service.EmailService;
import com.smaile.health.service.EmailTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Azure Communication Services Email Implementation
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class AzureEmailServiceImpl implements EmailService {

    // Constants
    private static final String HTML_CONTENT_TYPE = "text/html";
    private static final int EMAIL_THREAD_POOL_SIZE = 10;
    private static final String SDK_COMPATIBILITY_ERROR =
            "Azure Email SDK method not available. Please check SDK version compatibility.";

    private final EmailClient emailClient;
    private final EmailConfig emailConfig;
    private final AppConfig appConfig;
    private final EmailTemplateService emailTemplateService;
    private final Executor emailExecutor = Executors.newFixedThreadPool(EMAIL_THREAD_POOL_SIZE);

    @Override
    public EmailResponse sendEmail(EmailRequest emailRequest) {
        return performEmailSend(emailRequest, false);
    }

    @Override
    public CompletableFuture<EmailResponse> sendEmailAsync(EmailRequest emailRequest) {
        return CompletableFuture.supplyAsync(() -> performEmailSend(emailRequest, true), emailExecutor);
    }

    @Override
    public List<CompletableFuture<EmailResponse>> sendBulkEmailsAsync(List<EmailRequest> emailRequests) {
        List<CompletableFuture<EmailResponse>> futures = new ArrayList<>();
        for (EmailRequest request : emailRequests) {
            futures.add(sendEmailAsync(request));
        }
        return futures;
    }

    @Override
    public EmailResponse sendTemplatedEmail(String templateId,
                                            java.util.Map<String, Object> templateData,
                                            List<String> to,
                                            String subject) {
        try {
            String processedContent = emailTemplateService.processTemplate(templateId, templateData);

            if (processedContent == null || processedContent.startsWith("Template not found:") || processedContent.startsWith("Error processing template:")) {
                log.error("Failed to process template: {}", templateId);
                return EmailResponse.failure("Failed to process template: " + templateId, to, subject);
            }

            EmailRequest emailRequest = EmailRequest.builder()
                    .to(to)
                    .subject(subject)
                    .templateId(templateId)
                    .templateData(templateData)
                    .content(processedContent)
                    .contentType(HTML_CONTENT_TYPE)
                    .build();

            log.info("Sending templated email with template: {}, to: {}, subject: {}", templateId, to, subject);
            return sendEmail(emailRequest);

        } catch (Exception e) {
            log.error("Error sending templated email with template {}: {}", templateId, e.getMessage(), e);
            return EmailResponse.failure("Error sending email: " + e.getMessage(), to, subject);
        }
    }

    /**
     * Comprehensive email sending logic that handles all email operations
     */
    private EmailResponse performEmailSend(EmailRequest emailRequest, boolean isAsync) {
        String logPrefix = isAsync ? "Sending email asynchronously" : "Sending email";
        log.info("{} to: {}, subject: {}", logPrefix, emailRequest.getTo(), emailRequest.getSubject());

        try {
            // Build email message
            EmailMessage emailMessage = new EmailMessage();

            // Set sender
            emailMessage.setSenderAddress(emailConfig.getAzure().getSenderAddress());

            // Set recipients
            if (emailRequest.getTo() != null && !emailRequest.getTo().isEmpty()) {
                EmailAddress[] addresses = emailRequest.getTo().stream()
                        .map(EmailAddress::new)
                        .toArray(EmailAddress[]::new);
                emailMessage.setToRecipients(addresses);
            }

            if (emailRequest.getCc() != null && !emailRequest.getCc().isEmpty()) {
                EmailAddress[] addresses = emailRequest.getCc().stream()
                        .map(EmailAddress::new)
                        .toArray(EmailAddress[]::new);
                emailMessage.setCcRecipients(addresses);
            }

            if (emailRequest.getBcc() != null && !emailRequest.getBcc().isEmpty()) {
                EmailAddress[] addresses = emailRequest.getBcc().stream()
                        .map(EmailAddress::new)
                        .toArray(EmailAddress[]::new);
                emailMessage.setBccRecipients(addresses);
            }

            // Set content
            emailMessage.setSubject(emailRequest.getSubject());
            if (HTML_CONTENT_TYPE.equalsIgnoreCase(emailRequest.getContentType())) {
                emailMessage.setBodyHtml(emailRequest.getContent());
            } else {
                emailMessage.setBodyPlainText(emailRequest.getContent());
            }

            // Set attachments
            if (emailRequest.getAttachments() != null && !emailRequest.getAttachments().isEmpty()) {
                List<EmailAttachment> azureAttachments = new ArrayList<>();

                for (EmailRequest.EmailAttachment attachment : emailRequest.getAttachments()) {
                    BinaryData attachmentContent = null;

                    if (attachment.getContent() != null) {
                        attachmentContent = BinaryData.fromBytes(attachment.getContent());
                    } else if (attachment.getBase64Content() != null) {
                        attachmentContent = BinaryData.fromString(attachment.getBase64Content());
                    }

                    if (attachmentContent != null) {
                        EmailAttachment azureAttachment = new EmailAttachment(
                                attachment.getFileName(),
                                attachment.getContentType(),
                                attachmentContent
                        );
                        azureAttachments.add(azureAttachment);
                    } else {
                        log.warn("Attachment {} has no content, skipping", attachment.getFileName());
                    }
                }

                if (!azureAttachments.isEmpty()) {
                    emailMessage.setAttachments(azureAttachments.toArray(new EmailAttachment[0]));
                }
            }

            // Send to Azure
            EmailSendResult result;
            try {
                SyncPoller<EmailSendResult, EmailSendResult> poller = emailClient.beginSend(emailMessage);
                result = poller.getFinalResult();
            } catch (NoSuchMethodError | AbstractMethodError e) {
                log.warn("beginSend method not available: {}", e.getMessage());
                throw new UnsupportedOperationException(SDK_COMPATIBILITY_ERROR, e);
            }

            // Handle result
            if (result.getStatus() == EmailSendStatus.SUCCEEDED) {
                log.info("Email sent successfully with message ID: {}", result.getId());
                return EmailResponse.success(result.getId(), emailRequest.getTo(), emailRequest.getSubject());
            } else {
                log.error("Failed to send email. Status: {}", result.getStatus());
                return EmailResponse.failure(
                        "Email send failed with status: " + result.getStatus(),
                        emailRequest.getTo(),
                        emailRequest.getSubject()
                );
            }

        } catch (Exception e) {
            log.error("Error sending email to {}: {}", emailRequest.getTo(), e.getMessage(), e);
            return EmailResponse.failure(
                    "Error sending email: " + e.getMessage(),
                    emailRequest.getTo(),
                    emailRequest.getSubject()
            );
        }
    }

    @LogExecution
    @Override
    public void sendOrganizationOnboardNotification(UserDTO userDTO, Organization organization, SmaileUserCredential userCredential) {
        try {
            // Prepare email template data
            Map<String, Object> templateData = new HashMap<>();
            templateData.put("userName", userDTO.getFullName());
            templateData.put("organizationName", organization.getName());
            templateData.put("organizationType", organization.getType().getDescription());
            templateData.put("roleList", Optional.ofNullable(userDTO.getRoleCode()).map(RoleEnum::resolve).map(RoleEnum::getDescription).orElse("UNKNOWN"));
            templateData.put("temporaryPassword", userCredential.getPassword());
            templateData.put("loginUrl", appConfig.getLoginUrl());
            templateData.put("supportEmail", emailConfig.getSupport().getEmail());
            templateData.put("supportPhone", emailConfig.getSupport().getPhone());

            // Send templated email
            sendTemplatedEmail(
                    "organization-creation-notification",
                    templateData,
                    List.of(userDTO.getEmail()),
                    "New Organization Created – " + organization.getName() + " and Your Account Are Now Active"
            );

            log.info("Organization creation notification email sent to user: {}", userDTO.getEmail());
        } catch (Exception e) {
            log.error("Failed to send organization creation email: {}", e.getMessage(), e);
            throw new SmaileRuntimeException("Failed to send organization creation email: " + e.getMessage());
        }
    }
}
