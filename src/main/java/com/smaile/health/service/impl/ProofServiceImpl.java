package com.smaile.health.service.impl;

import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.MessageKey;
import com.smaile.health.constants.Status;
import com.smaile.health.domain.Proof;
import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.ProofMapper;
import com.smaile.health.model.ProofDTO;
import com.smaile.health.model.ProofSummaryDTO;
import com.smaile.health.model.ProofSummaryResponseDTO;
import com.smaile.health.model.request.CreateProofRequestDTO;
import com.smaile.health.repository.ProofRepository;
import com.smaile.health.repository.specification.ProofSpecification;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ProofService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class ProofServiceImpl implements ProofService {

    private final ProofRepository proofRepository;
    private final ProofMapper proofMapper;
    private final I18nService i18nService;

    /**
     Helper method to calculate and set the status of a proof based on isActive, effectiveDate, and cancellationDate

     @param proof the proof entity to update
     */
    private void calculateAndSetStatus(Proof proof) {
        if (!proof.getIsActive()) {
            proof.setStatus(Status.INACTIVE);
        } else if (proof.getEffectiveDate() != null && proof.getEffectiveDate().isAfter(LocalDate.now())) {
            // Proof is not yet effective - treat as ACTIVE
            proof.setStatus(Status.ACTIVE);
        } else if (proof.getCancellationDate() != null && proof.getCancellationDate().isBefore(LocalDate.now())) {
            // Proof has expired
            proof.setStatus(Status.EXPIRED);
        } else {
            // Proof is currently effective and not expired
            proof.setStatus(Status.ACTIVE);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Proof> getValidProofs(List<UUID> proofIds) {
        if (proofIds == null || proofIds.isEmpty()) {
            return List.of();
        }

        log.debug("Validating and retrieving {} proof IDs", proofIds.size());

        List<Proof> proofs = proofRepository.findAllById(proofIds);

        // Check if all IDs were found
        if (proofs.size() != proofIds.size()) {
            log.warn("Some proof IDs were not found. Expected: {}, Found: {}",
                    proofIds.size(), proofs.size());
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.PROOF_NOT_FOUND.getMessageKey())
            );
        }

        // Calculate status for all proofs
        proofs.forEach(this::calculateAndSetStatus);

        // Check if all proofs are active and not expired
        boolean hasInactiveOrExpiredProof = proofs.stream()
                .anyMatch(proof -> !proof.getIsActive() || proof.getStatus() == Status.EXPIRED);

        if (hasInactiveOrExpiredProof) {
            log.warn("Some proof IDs are inactive or expired");
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.PROOF_INACTIVE.getMessageKey())
            );
        }

        log.debug("All {} proof IDs are valid and active", proofIds.size());
        return proofs;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ProofDTO> search(String filter, Pageable pageable) {
        long startTime = System.currentTimeMillis();
        log.debug("Starting proof search operation with filter - Filter: {}, Page: {}",
                filter, pageable.getPageNumber());

        try {
            Specification<Proof> spec = ProofSpecification.searchSpecification(filter);
            Page<Proof> proofs = proofRepository.findAll(spec, pageable);

            // Calculate status for all proofs in the page
            proofs.getContent().forEach(this::calculateAndSetStatus);

            long endTime = System.currentTimeMillis();
            log.debug("Proof search operation completed in {} ms, found {} results",
                    endTime - startTime, proofs.getTotalElements());

            return proofs.map(proofMapper::toDTO);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Proof search operation failed after {} ms with error: {}", endTime - startTime, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ProofDTO> search(String filter, Status status, Pageable pageable) {
        long startTime = System.currentTimeMillis();
        log.debug("Starting proof search operation with filter - Filter: {}, Status: {}, Page: {}",
                filter, status, pageable.getPageNumber());

        try {
            Specification<Proof> spec = ProofSpecification.searchSpecificationWithStatus(filter, status);
            Page<Proof> proofs = proofRepository.findAll(spec, pageable);

            // Calculate status for all proofs in the page
            proofs.getContent().forEach(this::calculateAndSetStatus);

            long endTime = System.currentTimeMillis();
            log.debug("Proof search operation completed in {} ms, found {} results",
                    endTime - startTime, proofs.getTotalElements());

            return proofs.map(proofMapper::toDTO);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Proof search operation failed after {} ms with error: {}", endTime - startTime, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ProofDTO get(UUID id) {
        log.debug("Retrieving proof with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            Proof proof = proofRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.PROOF_NOT_FOUND.getMessageKey())
                    ));

            calculateAndSetStatus(proof);

            long endTime = System.currentTimeMillis();
            log.info("Successfully retrieved proof: {} (ID: {}) in {} ms",
                    proof.getCode(), proof.getId(), endTime - startTime);

            return proofMapper.toDTO(proof);
        } catch (NotFoundException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to retrieve proof with ID: {} after {} ms with error: {}", id, endTime - startTime, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public UUID create(CreateProofRequestDTO requestDTO) {
        log.debug("Creating new proof: {}", requestDTO.getCode());
        long startTime = System.currentTimeMillis();

        try {
            // Validate code uniqueness
            if (proofRepository.findByCodeIgnoreCase(requestDTO.getCode()).isPresent()) {
                log.warn("Attempted to create proof with duplicate code: {}", requestDTO.getCode());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.PROOF_CODE_DUPLICATE.getMessageKey())
                );
            }

            Proof proof = proofMapper.toEntity(requestDTO);
            calculateAndSetStatus(proof);
            Proof savedProof = proofRepository.save(proof);

            // Check if the proof became EXPIRED due to cancellation date
            if (savedProof.getStatus() == Status.EXPIRED) {
                log.warn("Created proof with EXPIRED status due to past cancellation date: {}", savedProof.getCode());
            }

            long endTime = System.currentTimeMillis();
            log.info("Successfully created proof: {} (ID: {}) in {} ms",
                    savedProof.getCode(), savedProof.getId(), endTime - startTime);

            return savedProof.getId();
        } catch (ValidationException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to create proof: {} after {} ms with error: {}", requestDTO.getCode(), endTime - startTime, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void update(UUID id, ProofDTO proofDTO) {
        log.debug("Updating proof with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            Proof existingProof = proofRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.PROOF_NOT_FOUND.getMessageKey())
                    ));

            if (!existingProof.getIsActive()) {
                log.warn("Attempted to update inactive proof with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.PROOF_CANNOT_UPDATE_INACTIVE.getKey())
                );
            }

            // Check if proof is EXPIRED and warn about updating
            if (existingProof.getStatus() == Status.EXPIRED) {
                log.warn("Attempted to update EXPIRED proof with ID: {}", id);
                // Allow update but log warning - business may want to reactivate expired proofs
            }

            // Validate code duplication (excluding current entity)
            if (proofRepository.findByCodeIgnoreCaseAndIdNot(proofDTO.getCode(), id).isPresent()) {
                log.warn("Attempted to update proof with duplicate code: {}", proofDTO.getCode());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.PROOF_CODE_DUPLICATE.getMessageKey())
                );
            }

            log.debug("Updating proof entity: {} -> {}",
                    existingProof.getCode(), proofDTO.getCode());

            proofDTO.setId(id);
            proofMapper.updateEntityFromDTO(proofDTO, existingProof);
            calculateAndSetStatus(existingProof);
            Proof updatedProof = proofRepository.save(existingProof);

            // Check if the proof became EXPIRED due to cancellation date
            if (updatedProof.getStatus() == Status.EXPIRED) {
                log.warn("Updated proof with EXPIRED status due to past cancellation date: {}", updatedProof.getCode());
            }

            long endTime = System.currentTimeMillis();
            log.info("Successfully updated proof: {} (ID: {}) in {} ms",
                    existingProof.getCode(), existingProof.getId(), endTime - startTime);

        } catch (ValidationException | NotFoundException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to update proof with ID: {} after {} ms with error: {}", id, endTime - startTime, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void delete(UUID id) {
        log.debug("Deactivating proof with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            Proof proof = proofRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.PROOF_NOT_FOUND.getMessageKey())
                    ));

            if (!proof.getIsActive()) {
                log.warn("Attempted to deactivate already inactive proof with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.PROOF_ALREADY_INACTIVE.getKey())
                );
            }

            proof.setIsActive(false);
            calculateAndSetStatus(proof);
            Proof deactivatedProof = proofRepository.save(proof);

            // Verify the proof is now INACTIVE
            if (deactivatedProof.getStatus() != Status.INACTIVE) {
                log.warn("Expected INACTIVE status after deactivation, but got: {} for proof: {}",
                        deactivatedProof.getStatus(), deactivatedProof.getCode());
            }

            long endTime = System.currentTimeMillis();
            log.info("Successfully deactivated proof: {} (ID: {}) in {} ms",
                    proof.getCode(), proof.getId(), endTime - startTime);

        } catch (ValidationException | NotFoundException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to deactivate proof with ID: {} after {} ms with error: {}", id, endTime - startTime, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProofSummaryDTO> getActiveProofs() {
        log.debug("Retrieving all active proofs");
        long startTime = System.currentTimeMillis();

        try {
            List<Proof> activeProofs = proofRepository.findByIsActiveTrue();
            // Calculate status for all proofs and filter out expired ones - only return truly active ones
            List<ProofSummaryDTO> summaryDTOs = activeProofs.stream()
                    .peek(this::calculateAndSetStatus)
                    .filter(proof -> proof.getStatus() == Status.ACTIVE)
                    .map(proofMapper::toSummaryDTO)
                    .toList();

            long endTime = System.currentTimeMillis();
            log.debug("Successfully retrieved {} active proofs in {} ms",
                    summaryDTOs.size(), endTime - startTime);

            return summaryDTOs;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to retrieve active proofs after {} ms with error: {}", endTime - startTime, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ProofSummaryResponseDTO getSummary() {
        log.debug("Retrieving proof summary statistics");
        long startTime = System.currentTimeMillis();

        try {
            // Get all proofs from database
            List<Proof> allProofs = proofRepository.findAll();

            // Calculate status for all proofs
            allProofs.forEach(this::calculateAndSetStatus);

            // Filter out deleted proofs
            List<Proof> nonDeletedProofs = allProofs.stream()
                    .filter(proof -> proof.getStatus() != Status.DELETED)
                    .toList();

            // Count proofs by status (excluding deleted)
            long totalProofs = nonDeletedProofs.size();
            long activeProofs = nonDeletedProofs.stream().mapToLong(proof -> proof.getStatus() == Status.ACTIVE ? 1 : 0).sum();
            long inactiveProofs = nonDeletedProofs.stream().mapToLong(proof -> proof.getStatus() == Status.INACTIVE ? 1 : 0).sum();
            long expiredProofs = nonDeletedProofs.stream().mapToLong(proof -> proof.getStatus() == Status.EXPIRED ? 1 : 0).sum();

            ProofSummaryResponseDTO summary = ProofSummaryResponseDTO.builder()
                    .totalProofs(totalProofs)
                    .activeProofs(activeProofs)
                    .inactiveProofs(inactiveProofs)
                    .expiredProofs(expiredProofs)
                    .build();

            long endTime = System.currentTimeMillis();
            log.debug("Successfully retrieved proof summary in {} ms - Total: {}, Active: {}, Inactive: {}, Expired: {}",
                    endTime - startTime, totalProofs, activeProofs, inactiveProofs, expiredProofs);

            return summary;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to retrieve proof summary after {} ms with error: {}", endTime - startTime, e.getMessage(), e);
            throw e;
        }
    }
}
