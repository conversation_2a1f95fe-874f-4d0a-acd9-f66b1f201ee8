package com.smaile.health.service.impl;

import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.Status;
import com.smaile.health.domain.Agreement;
import com.smaile.health.domain.AgreementProceduresDetail;
import com.smaile.health.domain.MedicalProvider;
import com.smaile.health.domain.Procedure;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.AgreementMapper;
import com.smaile.health.mapper.MedicalProviderMapper;
import com.smaile.health.mapper.ProcedureMapper;
import com.smaile.health.model.AgreementDTO;
import com.smaile.health.model.AgreementProcedureDTO;
import com.smaile.health.model.request.AgreementProcedureRequest;
import com.smaile.health.model.request.CreateAgreementDTO;
import com.smaile.health.model.request.Filter;
import com.smaile.health.model.request.UpdateAgreementDTO;
import com.smaile.health.model.request.UpdateAgreementProcedureRequest;
import com.smaile.health.repository.AgreementRepository;
import com.smaile.health.repository.MedicalProviderRepository;
import com.smaile.health.repository.ProcedureRepository;
import com.smaile.health.repository.specification.AgreementSpecification;
import com.smaile.health.service.AgreementService;
import com.smaile.health.service.I18nService;
import com.smaile.health.util.PageResponse;
import com.smaile.health.util.SecurityUtils;
import com.smaile.health.util.UUIDv7;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class AgreementServiceImpl implements AgreementService {

    private final I18nService i18nService;
    private final MedicalProviderRepository medicalProviderRepository;
    private final AgreementRepository agreementRepository;
    private final ProcedureRepository procedureRepository;
    private final AgreementMapper agreementMapper;
    private final MedicalProviderMapper medicalProviderMapper;
    private final ProcedureMapper procedureMapper;

    @PersistenceContext
    private EntityManager entityManager;

    public AgreementServiceImpl(I18nService i18nService,
                                MedicalProviderRepository medicalProviderRepository,
                                AgreementRepository agreementRepository,
                                ProcedureRepository procedureRepository,
                                AgreementMapper agreementMapper,
                                MedicalProviderMapper medicalProviderMapper,
                                ProcedureMapper procedureMapper) {
        this.i18nService = i18nService;
        this.medicalProviderRepository = medicalProviderRepository;
        this.agreementRepository = agreementRepository;
        this.procedureRepository = procedureRepository;
        this.agreementMapper = agreementMapper;
        this.medicalProviderMapper = medicalProviderMapper;
        this.procedureMapper = procedureMapper;
    }

    @Override
    public PageResponse<AgreementDTO> query(String search, List<Filter> filters, Pageable pageable) {
        OrganizationType type = SecurityUtils.getActorOrganization().get().getType();
        Specification<Agreement> spec = AgreementSpecification.search(search)
                .and(AgreementSpecification.withFilter(filters));
        if (Objects.equals(OrganizationType.IC, type)) {
            spec.and(AgreementSpecification.withChildMedicalProvider(SecurityUtils.getActorOrgId()));
        }
        Page<Agreement> page = agreementRepository.findAll(spec, pageable);
        List<UUID> procedureIds = page.getContent().stream().flatMap(a -> a.getProceduresDetails().stream())
                .map(AgreementProceduresDetail::getProcedureId).distinct().toList();

        List<Procedure> proceduresWithProofs = procedureRepository.findAllById(procedureIds);
        Map<UUID, Procedure> procedureMap = proceduresWithProofs.stream()
                .collect(Collectors.toMap(Procedure::getId, Function.identity()));
        Page<AgreementDTO> pageResponse = page.map(agreement -> toAgreementDTO(agreement, procedureMap));
        return PageResponse.of(pageResponse);
    }

    @Override
    public AgreementDTO detail(UUID agreementId) {
        Agreement agreement = agreementRepository.findById(agreementId).orElseThrow(
                () -> new ValidationException(i18nService.getMessage(ErrorCode.AGREEMENT_NOT_FOUND.getMessageKey())));

        List<UUID> procedureIds = agreement.getProceduresDetails().stream()
                .map(AgreementProceduresDetail::getProcedureId).distinct().toList();

        Map<UUID, Procedure> procedureMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(agreement.getProceduresDetails())) {
            List<Procedure> proceduresWithProofs = procedureRepository.findAllById(procedureIds);
            procedureMap = proceduresWithProofs.stream()
                    .collect(Collectors.toMap(Procedure::getId, Function.identity()));
        }

        return toAgreementDTO(agreement, procedureMap);
    }

    @Override
    @Transactional
    public UUID create(CreateAgreementDTO agreementRequest) {
        MedicalProvider provider = medicalProviderRepository.findById(agreementRequest.getProviderId()).orElseThrow(
                () -> new ValidationException(i18nService.getMessage(ErrorCode.MP_NOT_FOUND.getMessageKey())));
        if (agreementRequest.getEffectiveTime().isAfter(agreementRequest.getCancellationTime())) {
            throw new ValidationException(i18nService.getMessage(ErrorCode.TIME_RANGE_INVALID.getMessageKey()));
        }
        if (agreementRepository.existsByContractId(agreementRequest.getContractId())) {
            throw new ValidationException(i18nService.getMessage(ErrorCode.CONTRACT_ID_DUPLICATE.getMessageKey()));
        }
        if (!provider.getParent().getId().equals(SecurityUtils.getCurrentUserOrganizationId().get())) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.NO_AUTHORIZED_CREATE_AGREEMENT_WITH_MP.getMessageKey(),
                            provider.getName()));
        }

        Agreement agreement = toAgreement(agreementRequest);
        agreement.setId(UUIDv7.generate());
        agreement.setProvider(provider);
        agreement.setStatus(Status.ACTIVE);
        if (agreementRequest.getProcedures() != null) {
            List<Procedure> procedures = procedureRepository.findAllById(
                    agreementRequest.getProcedures().stream().map(AgreementProcedureRequest::getProcedureId).toList());
            Map<UUID, Procedure> procedureMap = procedures.stream()
                    .collect(Collectors.toMap(Procedure::getId, Function.identity()));
            // TODO: I think we can compare the size of procedures and agreementRequest.getProcedures()
            // to determine the invalid for generic error message
            agreementRequest.getProcedures().forEach(agreementProcedureRequest -> {
                if (!procedureMap.containsKey(agreementProcedureRequest.getProcedureId())) {
                    throw new ValidationException(i18nService.getMessage(ErrorCode.PROCEDURE_NOT_FOUND.getMessageKey(),
                            agreementProcedureRequest.getProcedureId()));
                }
            });
            Map<UUID, BigDecimal> procedureCostMap = agreementRequest.getProcedures().stream().collect(
                    Collectors.toMap(AgreementProcedureRequest::getProcedureId, AgreementProcedureRequest::getCost));
            // Directly add procedure details using the helper method
            for (Procedure procedure : procedures) {
                BigDecimal cost = procedureCostMap.get(procedure.getId());
                agreement.addOrUpdateProcedureDetail(procedure, cost);
            }
        }
        Agreement savedAgreement = agreementRepository.save(agreement);
        return savedAgreement.getId();
    }

    @Override
    @Transactional
    public void update(UUID id, UpdateAgreementDTO agreementRequest) {
        Agreement agreement = agreementRepository.findById(id).orElseThrow(
                () -> new ValidationException(i18nService.getMessage(ErrorCode.AGREEMENT_NOT_FOUND.getMessageKey())));
        if (agreementRequest.getEffectiveTime().isAfter(agreementRequest.getCancellationTime())) {
            throw new ValidationException(i18nService.getMessage(ErrorCode.TIME_RANGE_INVALID.getMessageKey()));
        }

        List<Agreement> agreementsSameContractId = agreementRepository.findAllByContractId(
                agreementRequest.getContractId());
        agreementsSameContractId.removeIf(a -> a.getId().equals(id));
        if (!agreementsSameContractId.isEmpty()) {
            throw new ValidationException(i18nService.getMessage(ErrorCode.CONTRACT_ID_DUPLICATE.getMessageKey()));
        }

        agreement.setContractId(agreementRequest.getContractId());
        agreement.setStatus(agreementRequest.getStatus());
        agreement.setShortDescription(agreementRequest.getShortDescription());
        agreement.setLongDescription(agreementRequest.getLongDescription());
    }

    @Override
    @Transactional
    public void updateProcedure(UUID id, UpdateAgreementProcedureRequest agreementRequest) {
        Agreement agreement = agreementRepository.findById(id).orElseThrow(
                () -> new ValidationException(i18nService.getMessage(ErrorCode.AGREEMENT_NOT_FOUND.getMessageKey())));

        Map<UUID, BigDecimal> procedureIdToCostMap = agreementRequest.getProcedures().stream()
                .collect(Collectors.toMap(
                        AgreementProcedureRequest::getProcedureId,
                        AgreementProcedureRequest::getCost
                ));

        List<Procedure> procedures = procedureRepository.findAllById(procedureIdToCostMap.keySet());

        if (procedures.size() != procedureIdToCostMap.size()) {
            throw new ValidationException(i18nService.getMessage(ErrorCode.PROCEDURE_NOT_FOUND.getMessageKey()));
        }

        Map<Procedure, BigDecimal> procedureCostMap = procedures.stream()
                .collect(Collectors.toMap(
                        procedure -> procedure,
                        procedure -> procedureIdToCostMap.get(procedure.getId())
                ));
        List<AgreementProceduresDetail> updatedProcedureDetails = new ArrayList<>();
        for (Map.Entry<Procedure, BigDecimal> entry : procedureCostMap.entrySet()) {
            Procedure procedure = entry.getKey();
            BigDecimal cost = entry.getValue();

            AgreementProceduresDetail newDetail = new AgreementProceduresDetail(agreement, procedure, cost);
            updatedProcedureDetails.add(newDetail);
        }
        agreement.updateProceduresDetails(updatedProcedureDetails);
    }

    private AgreementDTO toAgreementDTO(Agreement agreement, Map<UUID, Procedure> procedureMap) {
        AgreementDTO agreementDTO = agreementMapper.toDTO(agreement);
        if (agreement.getProceduresDetails() != null) {
            agreementDTO.setProcedures(agreement.getProceduresDetails().stream().map(procedureDetail -> {
                AgreementProcedureDTO procedureDTO = new AgreementProcedureDTO();
                UUID procedureId = procedureDetail.getProcedureId();
                procedureDTO.setProcedureId(procedureId);
                procedureDTO.setCost(procedureDetail.getCost());
                procedureDTO.setCode(procedureMapper.toDTO(procedureMap.get(procedureId)).getCode());
                procedureDTO.setDescription(procedureMapper.toDTO(procedureMap.get(procedureId)).getDescription());
                procedureDTO.setMarketCost(procedureMapper.toDTO(procedureMap.get(procedureId)).getMarketCost());
                procedureDTO.setEffectiveDate(procedureMapper.toDTO(procedureMap.get(procedureId)).getEffectiveDate());
                procedureDTO.setCancellationDate(
                        procedureMapper.toDTO(procedureMap.get(procedureId)).getCancellationDate());
                return procedureDTO;
            }).toList());
        }
        if (agreement.getProvider() != null) {
            agreementDTO.setMedicalProvider(medicalProviderMapper.toSimpleDTO(agreement.getProvider()));
        }
        return agreementDTO;
    }

    private Agreement toAgreement(CreateAgreementDTO agreement) {
        return agreementMapper.toEntity(agreement);
    }

}
