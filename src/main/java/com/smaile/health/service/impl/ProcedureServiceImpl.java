package com.smaile.health.service.impl;

import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.MessageKey;
import com.smaile.health.domain.Diagnosis;
import com.smaile.health.domain.Procedure;
import com.smaile.health.domain.Speciality;
import com.smaile.health.domain.Proof;
import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.exception.InternalServerException;
import com.smaile.health.mapper.ProcedureMapper;
import com.smaile.health.model.ProcedureDTO;
import com.smaile.health.model.request.CreateProcedureRequestDTO;
import com.smaile.health.model.request.UpdateProcedureRequestDTO;
import com.smaile.health.repository.DiagnosisRepository;
import com.smaile.health.repository.ProcedureRepository;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ProcedureService;
import com.smaile.health.service.SpecialityService;
import com.smaile.health.service.ProofService;
import com.smaile.health.repository.specification.ProcedureSpecification;
import com.smaile.health.util.UUIDv7;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class ProcedureServiceImpl implements ProcedureService {

    private final ProcedureRepository procedureRepository;
    private final DiagnosisRepository diagnosisRepository;
    private final SpecialityService specialityService;
    private final ProofService proofService;
    private final ProcedureMapper procedureMapper;
    private final I18nService i18nService;

    @Override
    @Transactional(readOnly = true)
//    @PreAuthorize("hasAnyRole('SUPER_SMAILE_ADMIN', 'IC_ADMIN')")
    public Page<ProcedureDTO> search(String filter, Pageable pageable) {
        long startTime = System.currentTimeMillis();
        log.debug("Starting search operation with filter - Filter: {}, Page: {}",
                filter, pageable.getPageNumber());

        try {
            Specification<Procedure> spec = ProcedureSpecification.searchSpecification(filter);
            Page<Procedure> procedurePage = procedureRepository.findAll(spec, pageable);

            long endTime = System.currentTimeMillis();
            log.debug("Search operation completed in {} ms, found {} results",
                    endTime - startTime, procedurePage.getTotalElements());

            return procedurePage.map(procedureMapper::toDTO);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Search operation failed after {} ms with error: {}", endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.PROCEDURE_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public ProcedureDTO getProcedureById(UUID id) {
        log.debug("Fetching Procedure with ID: {}", id);

        try {
            Procedure procedure = procedureRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.PROCEDURE_NOT_FOUND.getMessageKey())
                    ));

            if (!procedure.getIsActive()) {
                log.warn("Attempted to access inactive Procedure with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.PROCEDURE_INACTIVE.getMessageKey())
                );
            }

            return procedureMapper.toDTO(procedure);
        } catch (NotFoundException | ValidationException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to retrieve Procedure with ID {}: {}", id, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.PROCEDURE_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public ProcedureDTO createProcedure(CreateProcedureRequestDTO requestDTO) {
        log.debug("Creating new Procedure: {}", requestDTO.getCode());
        long startTime = System.currentTimeMillis();

        try {
            // Validate code duplication
            if (procedureRepository.existsByCodeIgnoreCase(requestDTO.getCode())) {
                log.warn("Attempted to create Procedure with duplicate code: {}", requestDTO.getCode());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.PROCEDURE_CODE_DUPLICATE.getMessageKey())
                );
            }

            Procedure procedure = procedureMapper.toEntity(requestDTO);
            procedure.setId(UUIDv7.generate());

            // Validate and load related diagnoses if provided
            if (requestDTO.getRelatedDiagnosisIds() != null && !requestDTO.getRelatedDiagnosisIds().isEmpty()) {
                List<Diagnosis> relatedDiagnoses = diagnosisRepository.findAllById(requestDTO.getRelatedDiagnosisIds());
                if (relatedDiagnoses.size() != requestDTO.getRelatedDiagnosisIds().size()) {
                    throw new ValidationException(
                            i18nService.getMessage(ErrorCode.DIAGNOSIS_NOT_FOUND.getMessageKey())
                    );
                }
                procedure.setRelatedDiagnoses(relatedDiagnoses);
            }

            // Validate and load specialties if provided
            List<Speciality> specialties = specialityService.getValidSpecialties(requestDTO.getSpecialtyIds());
            if (!specialties.isEmpty()) {
                procedure.setSpecialties(specialties);
            }

            // Validate and load proofs if provided
            List<Proof> proofs = proofService.getValidProofs(requestDTO.getProofIds());
            if (!proofs.isEmpty()) {
                procedure.setProofs(proofs);
            }

            log.debug("Saving Procedure entity with ID: {}", procedure.getId());
            Procedure savedProcedure = procedureRepository.save(procedure);

            long endTime = System.currentTimeMillis();
            log.info("Successfully created Procedure: {} (ID: {}) in {} ms",
                    savedProcedure.getCode(), savedProcedure.getId(), endTime - startTime);

            return procedureMapper.toDTO(savedProcedure);
        } catch (ValidationException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to create Procedure after {} ms: {}", endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.PROCEDURE_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public ProcedureDTO updateProcedure(UUID id, UpdateProcedureRequestDTO requestDTO) {
        log.debug("Updating Procedure with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            Procedure existingProcedure = procedureRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.PROCEDURE_NOT_FOUND.getMessageKey())
                    ));

            if (!existingProcedure.getIsActive()) {
                log.warn("Attempted to update inactive Procedure with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.PROCEDURE_INACTIVE.getMessageKey())
                );
            }

            // Validate code duplication (excluding current entity)
            if (procedureRepository.existsByCodeIgnoreCaseAndIdNot(requestDTO.getCode(), id)) {
                log.warn("Attempted to update Procedure with duplicate code: {}", requestDTO.getCode());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.PROCEDURE_CODE_DUPLICATE.getMessageKey())
                );
            }

            log.debug("Updating Procedure entity: {} -> {}",
                    existingProcedure.getCode(), requestDTO.getCode());

            procedureMapper.updateEntity(requestDTO, existingProcedure);

            // Update related diagnoses if provided
            if (requestDTO.getRelatedDiagnosisIds() != null) {
                if (requestDTO.getRelatedDiagnosisIds().isEmpty()) {
                    existingProcedure.setRelatedDiagnoses(null);
                } else {
                    List<Diagnosis> relatedDiagnoses = diagnosisRepository.findAllById(requestDTO.getRelatedDiagnosisIds());
                    if (relatedDiagnoses.size() != requestDTO.getRelatedDiagnosisIds().size()) {
                        throw new ValidationException(
                                i18nService.getMessage(ErrorCode.DIAGNOSIS_NOT_FOUND.getMessageKey())
                        );
                    }
                    existingProcedure.setRelatedDiagnoses(relatedDiagnoses);
                }
            }

            // Update specialties if provided
            if (requestDTO.getSpecialtyIds() != null) {
                if (requestDTO.getSpecialtyIds().isEmpty()) {
                    existingProcedure.setSpecialties(null);
                } else {
                    List<Speciality> specialties = specialityService.getValidSpecialties(requestDTO.getSpecialtyIds());
                    existingProcedure.setSpecialties(specialties);
                }
            }

            // Update proofs if provided
            if (requestDTO.getProofIds() != null) {
                if (requestDTO.getProofIds().isEmpty()) {
                    existingProcedure.setProofs(null);
                } else {
                    List<Proof> proofs = proofService.getValidProofs(requestDTO.getProofIds());
                    existingProcedure.setProofs(proofs);
                }
            }

            Procedure savedProcedure = procedureRepository.save(existingProcedure);

            long endTime = System.currentTimeMillis();
            log.info("Successfully updated Procedure: {} (ID: {}) in {} ms",
                    savedProcedure.getCode(), savedProcedure.getId(), endTime - startTime);

            return procedureMapper.toDTO(savedProcedure);
        } catch (ValidationException | NotFoundException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to update Procedure with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.PROCEDURE_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public void deleteProcedure(UUID id) {
        log.debug("Deactivating Procedure with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            Procedure procedure = procedureRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.PROCEDURE_NOT_FOUND.getMessageKey())
                    ));

            if (!procedure.getIsActive()) {
                log.warn("Attempted to deactivate already inactive Procedure with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.PROCEDURE_ALREADY_INACTIVE.getMessageKey())
                );
            }

            log.debug("Deactivating Procedure: {} (Current Status: {})",
                    procedure.getCode(), procedure.getIsActive());

            procedure.setIsActive(false);
            procedureRepository.save(procedure);

            long endTime = System.currentTimeMillis();
            log.info("Successfully deactivated Procedure: {} (ID: {}) in {} ms",
                    procedure.getCode(), procedure.getId(), endTime - startTime);

        } catch (ValidationException | NotFoundException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to deactivate Procedure with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.PROCEDURE_OPERATION_FAILED.getKey()), e
            );
        }
    }

}
