package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.domain.Organization;
import com.smaile.health.model.OrganizationDTO;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.RoleRepository;
import com.smaile.health.service.OrganizationService;
import com.smaile.health.util.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.EnumMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static com.smaile.health.constants.Constants.SMAILE_ADMIN_ORG;

@Service
@Transactional(readOnly = true, rollbackFor = Exception.class)
@Slf4j
@RequiredArgsConstructor
public class OrganizationServiceImpl implements OrganizationService {

    private final OrganizationRepository organizationRepository;
    private final RoleRepository roleRepository;

    @Override
    public Organization getSmaileOrganization() {
        return organizationRepository.getReferenceById(UUID.fromString(SMAILE_ADMIN_ORG));
    }

    @Override
    public Set<UUID> getOrganizationWithChildrenIds(UUID organizationId) {

        Set<UUID> orgIdList = new HashSet<>(organizationRepository.findByParentIdAndIsDeletedFalse(organizationId)
                .stream()
                .map(Organization::getId)
                .toList());

        orgIdList.add(organizationId);
        return orgIdList;
    }

    @Override
    @LogExecution
    public PageResponse<OrganizationDTO> getAllOrganizationsPaged(Pageable pageable) {
        Map<OrganizationType, Set<RoleEnum>> organizationTypeToRoleMap = buildOrganizationTypeToRoleMap();

        // Get all organizations with pagination
        Page<Organization> allOrgsPage = organizationRepository.findAll(pageable);

        // Map to DTOs
        Page<OrganizationDTO> dtoPage = allOrgsPage.map(org -> mapToOrganizationDTO(org, organizationTypeToRoleMap));

        // Return PageResponse
        return PageResponse.of(dtoPage);
    }

    @Override
    @LogExecution
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #currentOrgId, 'organizations:*:read')")
    public PageResponse<OrganizationDTO> getCurrentOrgWithChildrenPaged(UUID currentOrgId, Pageable pageable) {
        Map<OrganizationType, Set<RoleEnum>> organizationTypeToRoleMap = buildOrganizationTypeToRoleMap();

        Organization currentOrg = organizationRepository.findById(currentOrgId)
                .orElseThrow(() -> new RuntimeException("Current organization not found"));

        Page<Organization> childOrgsPage = organizationRepository.findByParentId(currentOrgId, pageable);

        List<OrganizationDTO> allOrgs = new ArrayList<>();

        allOrgs.add(mapToOrganizationDTO(currentOrg, organizationTypeToRoleMap));

        childOrgsPage.getContent().forEach(childOrg ->
                allOrgs.add(mapToOrganizationDTO(childOrg, organizationTypeToRoleMap)));

        Page<OrganizationDTO> combinedPage = new PageImpl<>(
                allOrgs,
                pageable,
                allOrgs.size()
        );

        return PageResponse.of(combinedPage);
    }

    /**
     * Builds a map from organization type to set of roles
     */
    private Map<OrganizationType, Set<RoleEnum>> buildOrganizationTypeToRoleMap() {
        Map<OrganizationType, Set<RoleEnum>> organizationTypeToRoleMap = new EnumMap<>(OrganizationType.class);
        roleRepository.findAll().forEach(role ->
                organizationTypeToRoleMap.compute(role.getOrganizationType(), (k, v) -> {
                    if (v == null) {
                        v = new HashSet<>();
                    }
                    v.add(RoleEnum.resolve(role.getCode()));
                    return v;
                })
        );
        return organizationTypeToRoleMap;
    }

    /**
     * Maps an Organization entity to OrganizationDTO
     */
    private OrganizationDTO mapToOrganizationDTO(Organization org,
                                                 Map<OrganizationType, Set<RoleEnum>> organizationTypeToRoleMap) {
        return OrganizationDTO.builder()
                .id(org.getId())
                .code(org.getCode())
                .name(org.getName())
                .type(org.getType().toString())
                .status(org.getStatus())
                .registrationNumber(org.getRegistrationNumber())
                .contactPhone(org.getContactPhone())
                .contactEmail(org.getContactEmail())
                .address(org.getAddress())
                .orgRoleList(organizationTypeToRoleMap.getOrDefault(org.getType(), Set.of()))
                .parentId(org.getParent() != null ? org.getParent().getId() : null)
                .dateCreated(org.getDateCreated())
                .createdBy(org.getCreatedBy())
                .lastUpdated(org.getLastUpdated())
                .updatedBy(org.getUpdatedBy())
                .build();
    }

}
