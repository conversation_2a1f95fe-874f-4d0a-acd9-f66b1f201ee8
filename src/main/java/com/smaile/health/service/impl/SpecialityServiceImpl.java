package com.smaile.health.service.impl;

import com.smaile.health.constants.ErrorCode;
import com.smaile.health.domain.Speciality;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.SpecialityMapper;
import com.smaile.health.model.SpecialityDTO;
import com.smaile.health.model.request.CreateSpecialityRequestDTO;
import com.smaile.health.repository.SpecialityRepository;
import com.smaile.health.repository.specification.SpecialitySpecification;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.SpecialityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class SpecialityServiceImpl implements SpecialityService {

    private final SpecialityRepository specialityRepository;
    private final I18nService i18nService;
    private final SpecialityMapper specialityMapper;

    @Override
    @Transactional(readOnly = true)
//    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public Page<SpecialityDTO> search(String filter, Pageable pageable) {
        long startTime = System.currentTimeMillis();
        log.debug("Starting search operation with filter - Filter: {}, Page: {}",
                filter, pageable.getPageNumber());

        try {
            Specification<Speciality> spec = SpecialitySpecification.searchSpecification(filter);
            Page<Speciality> specialties = specialityRepository.findAll(spec, pageable);

            long endTime = System.currentTimeMillis();
            log.debug("Search operation completed in {} ms, found {} results",
                    endTime - startTime, specialties.getTotalElements());

            return specialties.map(specialityMapper::toDTO);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Search operation failed after {} ms: {}", endTime - startTime, e.getMessage(), e);
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.SPECIALITY_OPERATION_FAILED.getMessageKey())
            );
        }
    }

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public SpecialityDTO get(UUID id) {
        log.debug("Retrieving specialty with ID: {}", id);

        Speciality speciality = specialityRepository.findById(id)
                .orElseThrow(() -> new ValidationException(
                        i18nService.getMessage(ErrorCode.SPECIALITY_NOT_FOUND.getMessageKey())
                ));

        return specialityMapper.toDTO(speciality);
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public UUID create(CreateSpecialityRequestDTO requestDTO) {
        log.debug("Creating new specialty with name: {}", requestDTO.getName());

        // Check if specialty with same name already exists
        if (specialityRepository.findByNameIgnoreCaseAndIsActiveTrue(requestDTO.getName()).isPresent()) {
            log.warn("Attempted to create specialty with duplicate name: {}", requestDTO.getName());
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.SPECIALITY_CODE_ALREADY_EXISTS.getMessageKey())
            );
        }

        Speciality speciality = specialityMapper.toEntity(requestDTO);
        Speciality savedSpeciality = specialityRepository.save(speciality);
        log.info("Successfully created specialty with ID: {} and name: {}", savedSpeciality.getId(), savedSpeciality.getName());

        return savedSpeciality.getId();
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public void update(UUID id, SpecialityDTO specialityDTO) {
        log.debug("Updating specialty with ID: {}", id);

        Speciality existingSpeciality = specialityRepository.findById(id)
                .orElseThrow(() -> new ValidationException(
                        i18nService.getMessage(ErrorCode.SPECIALITY_NOT_FOUND.getMessageKey())
                ));

        // Check if another specialty with same name already exists (excluding current one)
        if (specialityDTO.getName() != null && !specialityDTO.getName().equals(existingSpeciality.getName())) {
            if (specialityRepository.findByNameIgnoreCaseAndIdNotAndIsActiveTrue(specialityDTO.getName(), id).isPresent()) {
                log.warn("Attempted to update specialty with duplicate name: {}", specialityDTO.getName());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.SPECIALITY_CODE_ALREADY_EXISTS.getMessageKey())
                );
            }
        }

        // Update the entity
        specialityMapper.updateEntityFromDTO(specialityDTO, existingSpeciality);
        specialityRepository.save(existingSpeciality);
        log.info("Successfully updated specialty with ID: {} and name: {}", existingSpeciality.getId(), existingSpeciality.getName());
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public void delete(UUID id) {
        log.debug("Deleting specialty with ID: {}", id);

        Speciality speciality = specialityRepository.findById(id)
                .orElseThrow(() -> new ValidationException(
                        i18nService.getMessage(ErrorCode.SPECIALITY_NOT_FOUND.getMessageKey())
                ));

        speciality.setIsActive(false);
        specialityRepository.save(speciality);
        log.info("Successfully deleted specialty with ID: {} and code: {}", speciality.getId(), speciality.getName());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Speciality> getValidSpecialties(List<UUID> specialtyIds) {
        if (specialtyIds == null || specialtyIds.isEmpty()) {
            return List.of();
        }

        log.debug("Validating and retrieving {} specialty IDs", specialtyIds.size());

        List<Speciality> specialties = specialityRepository.findAllById(specialtyIds);

        // Check if all IDs were found
        if (specialties.size() != specialtyIds.size()) {
            log.warn("Some specialty IDs were not found. Expected: {}, Found: {}",
                    specialtyIds.size(), specialties.size());
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.SPECIALITY_NOT_FOUND.getMessageKey())
            );
        }

        // Check if all specialties are active
        boolean hasInactiveSpecialty = specialties.stream()
                .anyMatch(specialty -> !specialty.getIsActive());

        if (hasInactiveSpecialty) {
            log.warn("Some specialty IDs are inactive");
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.SPECIALITY_INACTIVE.getMessageKey())
            );
        }

        log.debug("All {} specialty IDs are valid and active", specialtyIds.size());
        return specialties;
    }
}
