package com.smaile.health.service.impl;

import com.smaile.health.mapper.ProofMapper;
import com.smaile.health.mapper.SpecialityMapper;
import com.smaile.health.model.AuthorizedSpecialtyDTO;
import com.smaile.health.model.CountryDTO;
import com.smaile.health.model.MarketDTO;
import com.smaile.health.model.MarketSegmentDTO;
import com.smaile.health.model.SpecialityDTO;
import com.smaile.health.repository.CountryRepository;
import com.smaile.health.repository.MarketRepository;
import com.smaile.health.repository.MarketSegmentRepository;

import com.smaile.health.model.ProofDTO;
import com.smaile.health.repository.ProofRepository;
import com.smaile.health.repository.SpecialityRepository;
import com.smaile.health.service.UtilityService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class UtilityServiceImpl implements UtilityService {

    private final MarketRepository marketRepository;
    private final MarketSegmentRepository marketSegmentRepository;

    private final CountryRepository countryRepository;
    private final SpecialityRepository specialityRepository;
    private final ProofRepository proofRepository;
    private final ProofMapper proofMapper;
    private final SpecialityMapper specialityMapper;

    @Override
    public List<MarketDTO> getAllMarket() {
        return marketRepository.findAll().stream()
                .map(entity -> MarketDTO.builder()
                        .id(entity.getId())
                        .name(entity.getName())
                        .code(entity.getCode())
                        .build())
                .toList();
    }

    @Override
    public List<CountryDTO> getAllCountry() {
        return countryRepository.findAll().stream()
                .map(entity -> CountryDTO.builder().id(entity.getId())
                        .name(entity.getName())
                        .code(entity.getCode())
                        .build())
                .toList();
    }

    @Override
    public List<MarketSegmentDTO> getAllMarketSegment() {
        return marketSegmentRepository.findAll().stream()
                .map(entity -> MarketSegmentDTO.builder()
                        .id(entity.getId())
                        .name(entity.getName())
                        .code(entity.getCode())
                        .build())
                .toList();
    }

    @Override
    public List<SpecialityDTO> getAllProfessionalSpecialty() {
        return specialityRepository.findByIsActiveTrue().stream()
                .map(specialityMapper::toDTO)
                .toList();
    }

    @Override
    public List<SpecialityDTO> getAllAuthorizedSpecialties() {
        return specialityRepository.findByIsActiveTrue().stream()
                .map(specialityMapper::toDTO)
                .toList();
    }

    @Override
    public List<ProofDTO> getAllProofs() {
        return proofRepository.findByIsActiveTrue().stream()
                .map(proofMapper::toDTO)
                .toList();
    }
}
