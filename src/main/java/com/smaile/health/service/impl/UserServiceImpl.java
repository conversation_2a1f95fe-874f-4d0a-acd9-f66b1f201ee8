package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.config.AppConfig;
import com.smaile.health.config.CustomAuthentication;
import com.smaile.health.config.EmailConfig;
import com.smaile.health.constants.Constants;
import com.smaile.health.constants.MessageKey;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.constants.SmaileConstant;
import com.smaile.health.constants.Status;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.Role;
import com.smaile.health.domain.User;
import com.smaile.health.domain.UserOrganization;
import com.smaile.health.domain.UserRole;
import com.smaile.health.domain.UsersSummaryProjection;
import com.smaile.health.exception.InternalServerException;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.UserMapper;
import com.smaile.health.model.SmaileUserCredential;
import com.smaile.health.model.UserDTO;
import com.smaile.health.model.UsersSummaryDTO;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.RoleRepository;
import com.smaile.health.repository.UserOrganizationRepository;
import com.smaile.health.repository.UserRepository;
import com.smaile.health.repository.UserRoleRepository;
import com.smaile.health.service.EmailService;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.IdentityProvider;
import com.smaile.health.service.KeycloakService;
import com.smaile.health.service.OrganizationService;
import com.smaile.health.service.UserService;
import com.smaile.health.util.EmailUtils;
import com.smaile.health.util.PasswordUtils;
import com.smaile.health.util.SecurityUtils;
import com.smaile.health.util.UUIDv7;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;


@Service
@Transactional(readOnly = true, rollbackFor = Exception.class)
@RequiredArgsConstructor
@Slf4j
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final UserOrganizationRepository userOrganizationRepository;
    private final UserRoleRepository userRoleRepository;
    private final RoleRepository roleRepository;
    private final OrganizationRepository organizationRepository;
    private final IdentityProvider identityProvider;
    private final KeycloakService keycloakService;
    private final I18nService i18nService;
    private final UserMapper userMapper;
    private final EmailService emailService;
    private final AppConfig appConfig;
    private final OrganizationService organizationService;
    private final EmailUtils emailUtils;
    private final EmailConfig emailConfig;

    @Override
    public CustomAuthentication getAuthenticationByKeycloakId(String keycloakId) {
        CustomAuthentication authentication = new CustomAuthentication();
        try {
            User user = userRepository.findOneByKeycloakId(keycloakId);
            if (user == null || !Objects.equals(keycloakId, user.getKeycloakId())) {
                log.warn("User with kcId = {} was not found", keycloakId);
                authentication.setAuthenticated(false);
                return authentication;
            }

            List<UserOrganization> validUserOrganization = user.getUserOrganizations().stream()
                    .filter(uo -> Objects.equals(uo.getStatus(), SmaileConstant.ACTIVE))
                    .toList();
            if (validUserOrganization.isEmpty()) {
                throw new SmaileRuntimeException("User has no active user-organization relation");
            }

            Set<String> roles = validUserOrganization.stream()
                    .flatMap(uo -> uo.getUserRoles().stream())
                    .filter(userRole -> Objects.equals(userRole.getStatus(), SmaileConstant.ACTIVE))
                    .map(UserRole::getRole)
                    .map(roleCode -> Constants.ROLE_PREFIX + roleCode.getCode())
                    .collect(Collectors.toSet());


            Map<UUID, Set<String>> organizationToPermissionsMap = buildOrganizationToPermissionsMap(validUserOrganization);

            // Clone each parent organization permissions to all corresponding child organizationsfix
            Map<UUID, Set<String>> fullOrgToPermissionMap = new HashMap<>(organizationToPermissionsMap);
            organizationToPermissionsMap.forEach((parentId, permissionSet) -> {
                List<Organization> childOrgList = organizationRepository.findAll((root, query, cb) -> {
                    Join<Organization, Organization> parent = root.join("parent", JoinType.LEFT);
                    return cb.equal(parent.get("id"), parentId);
                });
                childOrgList.forEach(child -> fullOrgToPermissionMap.put(child.getId(), new HashSet<>(permissionSet)));
            });

            log.debug("User keycloak id = {}, Roles found: [{}]", keycloakId, String.join(",", roles));
            authentication.setActor(user);
            authentication.setOrganizationToPermissionsMap(fullOrgToPermissionMap);
            authentication.setRoles(roles);
            authentication.setAuthenticated(true);
        } catch (Exception e) {
            log.error(e.getMessage());
            authentication.setAuthenticated(false);
        }
        return authentication;
    }

    @Override
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #orgId, 'users:*:create')")
    @LogExecution
    @Transactional
    public SmaileUserCredential createWithPasswordGenerated(UUID orgId, UserDTO userDTO, boolean notification, boolean hasTempPassword) {
        Organization organization = organizationRepository.findById(orgId)
                .orElseThrow(() -> new SmaileRuntimeException("Cannot find organization %s".formatted(orgId)));
        String roleCode = userDTO.getRoleCode();
        String orgTypeCode = organization.getType().name();
        if (roleCode.length() <= orgTypeCode.length()
                || !roleCode.startsWith(orgTypeCode)
                || roleCode.substring(orgTypeCode.length() + 1).contains("_")) {
            throw new SmaileRuntimeException("Cannot create user with role = %s for Organization %s [%s]"
                    .formatted(roleCode, organization.getId(), orgTypeCode));
        }

        String password = PasswordUtils.generateTemporaryPassword(8);
        UUID userId = createUserInternal(orgId, userDTO, password, notification, hasTempPassword);
        return SmaileUserCredential.builder().id(userId).password(password).build();
    }

    @Override
    public UserDTO getAboutMe() {
        return get(SecurityUtils.getActorContext().getActor().getId());
    }

    @Override
    @LogExecution
    public boolean isAvailableToCreate(UserDTO userDto) {
        if (userRepository.findOneByEmail(userDto.getEmail()).isPresent()) {
            return false;
        }
        return identityProvider.findByEmailAndUsername(userDto.getEmail(), userDto.getUsername()).isEmpty();
    }

    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #organizationId, 'users:*:read')")
    @LogExecution
    @Override
    public Page<UserDTO> queryUserByCriteria(UUID organizationId, String partialNameOrEmail, RoleEnum role, Status status, Pageable pageable) {
        Page<User> userPage = userRepository.findAll((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (organizationId != null) {
                Set<UUID> orgIdList = organizationService.getOrganizationWithChildrenIds(organizationId);
                predicates.add(root.get("organization").get("id").in(orgIdList));
            }

            addCommonUserPredicates(predicates, root, cb, partialNameOrEmail, role, status);

            return predicates.stream().reduce(cb::and).orElseGet(cb::conjunction);
        }, pageable);
        return userPage.map(userMapper::toDTO);
    }

    @Override
    @LogExecution
    public UsersSummaryDTO queryUsersSummaryData() {
        UUID actorOrgId = null;
        if (!SecurityUtils.hasRole(RoleEnum.SUPER_SMAILE_ADMIN.name())) {
            actorOrgId = SecurityUtils.getCurrentUserOrganizationId().get();
        }
        UsersSummaryProjection usersSummary = userRepository.getUsersSummary(actorOrgId);
        return UsersSummaryDTO.builder()
                .totalUsers(usersSummary.getTotalUsers())
                .activeUsers(usersSummary.getActiveUsers())
                .inactiveUsers(usersSummary.getInactiveUsers())
                .totalOrganizations(usersSummary.getTotalOrganizations())
                .build();
    }

    @Override
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #orgId, 'users:*:update')")
    @LogExecution
    public UUID resetPassword(UUID orgId, UserDTO userDTO) {
        String newPassword = PasswordUtils.generateTemporaryPassword(8);
        identityProvider.resetPassword(userDTO.getKeycloakId(), newPassword);

        String subject = "Your Temporary Password for Account Access";
        Map<String, Object> contentData = Map.of(
                "userName", userDTO.getFullName(),
                "temporaryPassword", newPassword,
                "loginUrl", appConfig.getLoginUrl(),
                "supportEmail", emailConfig.getSupport().getEmail(),
                "supportPhone", emailConfig.getSupport().getPhone()
        );
        emailService.sendTemplatedEmail("user-reset-password", contentData, List.of(userDTO.getEmail()), subject);
        return userDTO.getId();
    }

    /**
     @param scopedOrgId Organization ID where the user will be created
     @param userDto     User data transfer object
     @param password    Optional password (for professional users or temporary passwords)
     @return Created user ID
     */
    private UUID createUserInternal(UUID scopedOrgId, final UserDTO userDto, String password,
                                    boolean notification, boolean hasTempPassword) {
        // Validate organization
        Organization organization = validateAndGetOrganization(scopedOrgId, null);

        // Validate user doesn't already exist
        validateUserDoesNotExist(userDto.getEmail());

        // Get or determine role
        Role role = getOrDetermineRole(userDto, null);

        if (Strings.CS.endsWith(userDto.getRoleCode(), SmaileConstant.ADMIN) && userDto.getPhone() == null) {
            throw new SmaileRuntimeException("User with empty phone info cannot be initialized as %s".formatted(userDto.getRoleCode()));
        }

        // Create user in identity provider
        String kcUserId = createUserInIdentityProvider(userDto, password, hasTempPassword);

        // Create user entity and relationships
        User user = createUserEntity(userDto, kcUserId, organization);
        createUserRelationships(user, organization, role);

        if (notification) {
            // TODO: consider do this Async
            emailUtils.sendTemplatedEmail("user-creation-notification",
                    Map.of(
                            "userName", user.getFullName(),
                            "roleList", List.of(role.getName()),
                            "organizationName", organization.getName(),
                            "organizationType", organization.getType().getDescription(),
                            "temporaryPassword", Optional.ofNullable(password).orElse(""),
                            "loginUrl", appConfig.getLoginUrl(),
                            "supportEmail", emailConfig.getSupport().getEmail(),
                            "supportPhone", emailConfig.getSupport().getPhone()
                    ), List.of(user.getEmail()), "Welcome to SMAILE");
        }

        return user.getId();
    }

    @Override
    @Transactional
    public UUID createProfessionalUser(UUID orgId, UserDTO userDto, String password) {
        Organization organization = organizationRepository.findById(orgId)
                .orElseThrow(() -> new SmaileRuntimeException("No organization found for id [%s]".formatted(orgId)));
        if (!OrganizationType.PROFESSIONAL.equals(organization.getType())) {
            throw new SmaileRuntimeException("Organization with id [%s] is not a professional organization".formatted(orgId));
        }
        userRepository.findOneByEmail(userDto.getEmail())
                .ifPresent(u -> {
                    throw new SmaileRuntimeException("User with email [%s] existed".formatted(u.getEmail()));
                });
        Role role = roleRepository.findOneByCode(RoleEnum.PROFESSIONAL.name()).orElseThrow(() -> new SmaileRuntimeException("No role found for code %s".formatted(RoleEnum.PROFESSIONAL.name())));
        String kcUserId = identityProvider.createUserWithPassword(userDto, password, true);
        log.info("Created KC user id {} ", kcUserId);
        User user = User.builder()
                .id(UUIDv7.generate())
                .keycloakId(kcUserId)
                .email(userDto.getEmail())
                .username(userDto.getUsername())
                .fullName(userDto.getFullName())
                .status(userDto.getStatus())
                .organization(organization)
                .build();
        userRepository.save(user);

        UserOrganization userOrg = UserOrganization.builder()
                .id(UUIDv7.generate())
                .user(user)
                .organization(organization)
                .status(SmaileConstant.ACTIVE)
                .build();
        userOrganizationRepository.save(userOrg);

        UserRole userRole = UserRole.builder()
                .id(UUIDv7.generate())
                .userOrganization(userOrg)
                .role(role)
                .startTime(SmaileConstant.DEFAULT_START_TIME)
                .endTime(SmaileConstant.DEFAULT_END_TIME)
                .status(SmaileConstant.ACTIVE)
                .build();
        userRoleRepository.save(userRole);
        return user.getId();
    }

    @Override
    public void updateUser(UUID id, UserDTO userDto) {
        UUID organizationId = userRepository.findById(id)
                .map(User::getOrganization)
                .map(Organization::getId)
                .orElseThrow(() -> new SmaileRuntimeException("Cannot find primary organization for user %s".formatted(userDto.getId())));
        update(organizationId, id, userDto);
    }

    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #scopedOrgId, 'users:*:update')")
    private void update(final UUID scopedOrgId, final UUID userId, final UserDTO userDto) {
        User existingUser = userRepository.findById(userId)
                .filter(user -> user.getUserOrganizations().stream()
                        .filter(uo -> Objects.equals(uo.getStatus(), SmaileConstant.ACTIVE))
                        .anyMatch(uo -> Objects.equals(uo.getOrganization().getId(), scopedOrgId))
                )
                .orElseThrow(() -> new SmaileRuntimeException("User with id = %s not found for org %s".formatted(userId, scopedOrgId)));

        existingUser.setFullName(userDto.getFullName());
        existingUser.setStatus(userDto.getStatus());
        existingUser.setPhone(userDto.getPhone());

        userRepository.save(existingUser);
    }

    @Override
    @LogExecution
    public UserDTO get(final UUID id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new SmaileRuntimeException("User with id = %s not found".formatted(id)));
        return userMapper.toDTO(user);
    }

    /**
     * Builds organization to permissions map from user organizations
     */
    private Map<UUID, Set<String>> buildOrganizationToPermissionsMap(List<UserOrganization> validUserOrganization) {
        return validUserOrganization.stream()
                .filter(uo -> ObjectUtils.isNotEmpty(uo.getUserRoles()))
                .filter(uo -> uo.getUserRoles().stream()
                        .anyMatch(ur -> Objects.equals(ur.getStatus(), SmaileConstant.ACTIVE))
                )
                .collect(Collectors.toMap(
                        uo -> uo.getOrganization().getId(),
                        uo -> uo.getUserRoles().stream())
                )
                .entrySet().stream()
                .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry.getValue()
                                        .filter(userRole -> Objects.equals(userRole.getStatus(), SmaileConstant.ACTIVE))
                                        .map(UserRole::getRole)
                        )
                ).entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue()
                                .flatMap(role -> role.getPermissions()
                                        .stream().map(p -> "%s:%s:%s".formatted(p.getResource(), p.getSubResource(), p.getAction()))
                                ).collect(Collectors.toSet())));
    }

    /**
     * Adds common user predicates to the predicate list
     */
    private void addCommonUserPredicates(List<Predicate> predicates, jakarta.persistence.criteria.Root<User> root,
                                       jakarta.persistence.criteria.CriteriaBuilder cb, String partialNameOrEmail,
                                       RoleEnum role, Status status) {
        if (StringUtils.isNotBlank(partialNameOrEmail)) {
            String pattern = "%" + partialNameOrEmail.toLowerCase() + "%";
            predicates.add(cb.or(
                    cb.like(cb.lower(root.get("email")), pattern),
                    cb.like(cb.lower(root.get("fullName")), pattern)
            ));
        }
        if (status != null) {
            predicates.add(cb.equal(root.get("status"), status.name()));
        }
        if (role != null) {
            Join<User, UserOrganization> userOrgJoin = root.join("userOrganizations");
            Join<UserOrganization, UserRole> userRoleJoin = userOrgJoin.join("userRoles");
            Join<UserRole, Role> roleJoin = userRoleJoin.join("role");
            predicates.add(cb.equal(roleJoin.get("code"), role.name()));
        }
    }

    /**
     * Validates organization and returns it, with type validation for professional users
     */
    private Organization validateAndGetOrganization(UUID scopedOrgId, RoleEnum forcedRole) {
        Organization organization = organizationRepository.findById(scopedOrgId)
                .orElseThrow(() -> new SmaileRuntimeException("No organization found for id [%s]".formatted(scopedOrgId)));

        // Validate organization type for professional users
        if (forcedRole == RoleEnum.PROFESSIONAL && !OrganizationType.PROFESSIONAL.equals(organization.getType())) {
            throw new SmaileRuntimeException("Organization with id [%s] is not a professional organization".formatted(scopedOrgId));
        }

        return organization;
    }

    /**
     * Validates that user doesn't already exist
     */
    private void validateUserDoesNotExist(String email) {
        userRepository.findOneByEmail(email)
                .ifPresent(u -> {
                    throw new SmaileRuntimeException("User with email [%s] existed".formatted(u.getEmail()));
                });
    }

    /**
     * Gets or determines the role for the user
     */
    private Role getOrDetermineRole(UserDTO userDto, RoleEnum forcedRole) {
        if (forcedRole != null) {
            return roleRepository.findOneByCode(forcedRole.name())
                    .orElseThrow(() -> new SmaileRuntimeException("No role found for code %s".formatted(forcedRole.name())));
        } else {
            return roleRepository.findOneByCode(userDto.getRoleCode())
                    .orElseThrow(() -> new SmaileRuntimeException("No role found for code %s".formatted(userDto.getRoleCode())));
        }
    }

    /**
     * Creates user in identity provider
     */
    private String createUserInIdentityProvider(UserDTO userDto, String password, boolean hasTempPassword) {
        if (password != null && !password.isBlank()) {
            return identityProvider.createUserWithPassword(userDto, password, hasTempPassword);
        } else {
            return identityProvider.createUser(userDto);
        }
    }

    /**
     * Creates the user entity
     */
    private User createUserEntity(UserDTO userDto, String kcUserId, Organization organization) {
        User user = User.builder()
                .id(UUIDv7.generate())
                .keycloakId(kcUserId)
                .email(userDto.getEmail())
                .username(userDto.getUsername())
                .fullName(userDto.getFullName())
                .phone(userDto.getPhone())
                .status(userDto.getStatus())
                .organization(organization)
                .build();

        log.debug("Creating db user: {}", user);
        userRepository.save(user);
        log.debug("Smaile user created, smaile user id = {}", user.getId());

        return user;
    }

    /**
     * Creates user-organization and user-role relationships
     */
    private void createUserRelationships(User user, Organization organization, Role role) {
        UserOrganization userOrg = UserOrganization.builder()
                .id(UUIDv7.generate())
                .user(user)
                .organization(organization)
                .status(SmaileConstant.ACTIVE)
                .build();
        userOrganizationRepository.save(userOrg);

        // Create user-role relationship
        UserRole userRole = UserRole.builder()
                .id(UUIDv7.generate())
                .userOrganization(userOrg)
                .role(role)
                .startTime(SmaileConstant.DEFAULT_START_TIME)
                .endTime(SmaileConstant.DEFAULT_END_TIME)
                .status(SmaileConstant.ACTIVE)
                .build();
        userRoleRepository.save(userRole);
    }

    @Override
    public String getUserName(UUID userId) {
        if (userId == null) {
            return null;
        }
        return userRepository.findById(userId)
                .map(User::getFullName)
                .orElse(userId.toString()); // Fallback to UUID string if user not found
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #userId, 'users:*:update')")
    public void activate(UUID userId) {
        log.debug("Activating user with ID: {}", userId);
        long startTime = System.currentTimeMillis();

        try {
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new ValidationException(
                            i18nService.getMessage(MessageKey.USER_NOT_FOUND.getKey())
                    ));

            if (user.getStatus() == Status.ACTIVE) {
                log.warn("Attempted to activate already active user with ID: {}", userId);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.USER_ALREADY_ACTIVE.getKey())
                );
            }

            log.debug("Activating user: {} (Current Status: {})",
                    user.getFullName(), user.getStatus());

            user.setStatus(Status.ACTIVE);
            userRepository.save(user);

            keycloakService.enableUser(user.getKeycloakId());

            long endTime = System.currentTimeMillis();
            log.info("Successfully activated user: {} (ID: {}) in {} ms",
                    user.getFullName(), user.getId(), endTime - startTime);

        } catch (ValidationException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to activate user with ID {} after {} ms: {}",
                    userId, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.USER_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #userId, 'users:*:update')")
    public void deactivate(UUID userId) {
        log.debug("Deactivating user with ID: {}", userId);
        long startTime = System.currentTimeMillis();

        try {
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new ValidationException(
                            i18nService.getMessage(MessageKey.USER_NOT_FOUND.getKey())
                    ));

            if (user.getStatus() == Status.INACTIVE) {
                log.warn("Attempted to deactivate already inactive user with ID: {}", userId);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.USER_ALREADY_INACTIVE.getKey())
                );
            }

            log.debug("Deactivating user: {} (Current Status: {})",
                    user.getFullName(), user.getStatus());

            user.setStatus(Status.INACTIVE);
            userRepository.save(user);

            keycloakService.disableUser(user.getKeycloakId());

            long endTime = System.currentTimeMillis();
            log.info("Successfully deactivated user: {} (ID: {}) in {} ms",
                    user.getFullName(), user.getId(), endTime - startTime);

        } catch (ValidationException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to deactivate user with ID {} after {} ms: {}",
                    userId, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.USER_OPERATION_FAILED.getKey()), e
            );
        }
    }
}
