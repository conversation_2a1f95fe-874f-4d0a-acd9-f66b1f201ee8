package com.smaile.health.service.impl;

import com.smaile.health.service.EmailTemplateService;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * EmailTemplateBuilder - A service that builds and processes HTML email templates.
 */
@Service
@Log4j2
public class EmailTemplateBuilder implements EmailTemplateService {

    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{\\{(\\w+)\\}\\}");
    private static final String TEMPLATE_PATH = "templates/email/";
    
    // Cache for loaded templates
    private final Map<String, String> templateCache = new HashMap<>();

    @Override
    public String processTemplate(String templateId, Map<String, Object> templateData) {
        String template = loadTemplate(templateId);
        if (template == null) {
            log.warn("Template not found: {}", templateId);
            return "Template not found: " + templateId;
        }

        try {
            return replacePlaceholders(template, templateData);
        } catch (Exception e) {
            log.error("Error processing template {}: {}", templateId, e.getMessage(), e);
            return "Error processing template: " + e.getMessage();
        }
    }

    @Override
    public boolean templateExists(String templateId) {
        return loadTemplate(templateId) != null;
    }

    @Override
    public List<String> getAvailableTemplates() {
        // Return list of available template IDs based on what we know exists
        return List.of(
                "organization-creation-notification",
            "professional-registration-confirmation"
        );
    }

    private String loadTemplate(String templateId) {
        // Check cache first
        if (templateCache.containsKey(templateId)) {
            return templateCache.get(templateId);
        }

        try {
            String templatePath = TEMPLATE_PATH + templateId + ".html";
            ClassPathResource resource = new ClassPathResource(templatePath);
            
            if (!resource.exists()) {
                log.warn("Template file not found: {}", templatePath);
                return null;
            }

            // Read template content
            try (InputStreamReader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)) {
                String template = FileCopyUtils.copyToString(reader);
                
                // Cache the template
                templateCache.put(templateId, template);
                
                log.debug("Template loaded successfully: {}", templateId);
                return template;
            }
        } catch (IOException e) {
            log.error("Error loading template {}: {}", templateId, e.getMessage(), e);
            return null;
        }
    }

    private String replacePlaceholders(String template, Map<String, Object> data) {
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(template);
        StringBuilder result = new StringBuilder();

        while (matcher.find()) {
            String placeholder = matcher.group(1);
            Object value = data.get(placeholder);
            
            if (value != null) {
                matcher.appendReplacement(result, Matcher.quoteReplacement(value.toString()));
            } else {
                log.warn("Placeholder '{}' not found in template data", placeholder);
                matcher.appendReplacement(result, Matcher.quoteReplacement("{{" + placeholder + "}}"));
            }
        }
        
        matcher.appendTail(result);
        return result.toString();
    }
}
