package com.smaile.health.service.impl;

import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.MessageKey;
import com.smaile.health.domain.Diagnosis;
import com.smaile.health.domain.Speciality;
import com.smaile.health.domain.Proof;
import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.exception.InternalServerException;
import com.smaile.health.mapper.DiagnosisMapper;
import com.smaile.health.model.request.CreateDiagnosisRequestDTO;
import com.smaile.health.model.DiagnosisDTO;
import com.smaile.health.model.DiagnosisSummaryDTO;
import com.smaile.health.repository.DiagnosisRepository;
import com.smaile.health.service.DiagnosisService;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.SpecialityService;
import com.smaile.health.service.ProofService;
import com.smaile.health.repository.specification.DiagnosisSpecification;
import com.smaile.health.util.UUIDv7;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class DiagnosisServiceImpl implements DiagnosisService {

    private final DiagnosisRepository diagnosisRepository;
    private final SpecialityService specialityService;
    private final ProofService proofService;
    private final DiagnosisMapper diagnosisMapper;
    private final I18nService i18nService;

    @Override
    @Transactional(readOnly = true)
//    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public Page<DiagnosisDTO> search(String filter, Pageable pageable) {
        long startTime = System.currentTimeMillis();
        log.debug("Starting search operation with filter - Filter: {}, Page: {}",
                filter, pageable.getPageNumber());

        try {
            Specification<Diagnosis> spec = DiagnosisSpecification.searchSpecification(filter);
            Page<Diagnosis> diagnosisPage = diagnosisRepository.findAll(spec, pageable);

            long endTime = System.currentTimeMillis();
            log.debug("Search operation completed in {} ms, found {} results",
                    endTime - startTime, diagnosisPage.getTotalElements());

            return diagnosisPage.map(diagnosisMapper::toDTO);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Search operation failed after {} ms with error: {}", endTime - startTime, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public DiagnosisDTO get(UUID id) {
        log.debug("Fetching Diagnosis with ID: {}", id);

        try {
            Diagnosis diagnosis = diagnosisRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.DIAGNOSIS_NOT_FOUND.getMessageKey())
                    ));

            if (!diagnosis.getIsActive()) {
                log.warn("Attempted to access inactive Diagnosis with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.DIAGNOSIS_INACTIVE.getMessageKey())
                );
            }

            return diagnosisMapper.toDTO(diagnosis);
        } catch (NotFoundException | ValidationException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to retrieve Diagnosis with ID {}: {}", id, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.DIAGNOSIS_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public UUID create(CreateDiagnosisRequestDTO requestDTO) {
        log.debug("Creating new Diagnosis: {}", requestDTO.getCode());
        long startTime = System.currentTimeMillis();

        try {
            // Validate code duplication
            if (diagnosisRepository.findByCodeIgnoreCaseAndIsActiveTrue(requestDTO.getCode()).isPresent()) {
                log.warn("Attempted to create Diagnosis with duplicate code: {}", requestDTO.getCode());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.DIAGNOSIS_CODE_DUPLICATE.getMessageKey())
                );
            }

            Diagnosis diagnosis = diagnosisMapper.toEntity(requestDTO);
            diagnosis.setId(UUIDv7.generate());

            // Validate and load specialties if provided
            List<Speciality> specialties = specialityService.getValidSpecialties(requestDTO.getSpecialtyIds());
            if (!specialties.isEmpty()) {
                diagnosis.setSpecialties(specialties);
            }

            // Validate and load proofs if provided
            List<Proof> proofs = proofService.getValidProofs(requestDTO.getProofIds());
            if (!proofs.isEmpty()) {
                diagnosis.setProofs(proofs);
            }

            log.debug("Saving Diagnosis entity with ID: {}", diagnosis.getId());
            Diagnosis savedDiagnosis = diagnosisRepository.save(diagnosis);

            long endTime = System.currentTimeMillis();
            log.info("Successfully created Diagnosis: {} (ID: {}) in {} ms",
                    savedDiagnosis.getCode(), savedDiagnosis.getId(), endTime - startTime);

            return savedDiagnosis.getId();
        } catch (ValidationException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to create Diagnosis after {} ms: {}", endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.DIAGNOSIS_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public void update(UUID id, DiagnosisDTO diagnosisDTO) {
        log.debug("Updating Diagnosis with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            Diagnosis existingDiagnosis = diagnosisRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.DIAGNOSIS_NOT_FOUND.getMessageKey())
                    ));

            if (!existingDiagnosis.getIsActive()) {
                log.warn("Attempted to update inactive Diagnosis with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.DIAGNOSIS_INACTIVE.getMessageKey())
                );
            }

            // Validate code duplication (excluding current entity)
            if (diagnosisRepository.findByCodeIgnoreCaseAndIdNotAndIsActiveTrue(diagnosisDTO.getCode(), id).isPresent()) {
                log.warn("Attempted to update Diagnosis with duplicate code: {}", diagnosisDTO.getCode());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.DIAGNOSIS_CODE_DUPLICATE.getMessageKey())
                );
            }

            log.debug("Updating Diagnosis entity: {} -> {}",
                    existingDiagnosis.getCode(), diagnosisDTO.getCode());

            diagnosisDTO.setId(id);
            diagnosisMapper.updateEntityFromDTO(diagnosisDTO, existingDiagnosis);

            // Update specialties if provided
            if (diagnosisDTO.getSpecialtyIds() != null) {
                if (diagnosisDTO.getSpecialtyIds().isEmpty()) {
                    existingDiagnosis.setSpecialties(null);
                } else {
                    List<Speciality> specialties = specialityService.getValidSpecialties(diagnosisDTO.getSpecialtyIds());
                    existingDiagnosis.setSpecialties(specialties);
                }
            }

            // Update proofs if provided
            if (diagnosisDTO.getProofIds() != null) {
                if (diagnosisDTO.getProofIds().isEmpty()) {
                    existingDiagnosis.setProofs(null);
                } else {
                    List<Proof> proofs = proofService.getValidProofs(diagnosisDTO.getProofIds());
                    existingDiagnosis.setProofs(proofs);
                }
            }

            diagnosisRepository.save(existingDiagnosis);

            long endTime = System.currentTimeMillis();
            log.info("Successfully updated Diagnosis: {} (ID: {}) in {} ms",
                    existingDiagnosis.getCode(), existingDiagnosis.getId(), endTime - startTime);

        } catch (ValidationException | NotFoundException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to update Diagnosis with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.DIAGNOSIS_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public void delete(UUID id) {
        log.debug("Deactivating Diagnosis with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            Diagnosis diagnosis = diagnosisRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.DIAGNOSIS_NOT_FOUND.getMessageKey())
                    ));

            if (!diagnosis.getIsActive()) {
                log.warn("Attempted to deactivate already inactive Diagnosis with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.DIAGNOSIS_ALREADY_INACTIVE.getMessageKey())
                );
            }

            log.debug("Deactivating Diagnosis: {} (Current Status: {})",
                    diagnosis.getCode(), diagnosis.getIsActive());

            diagnosis.setIsActive(false);
            diagnosisRepository.save(diagnosis);

            long endTime = System.currentTimeMillis();
            log.info("Successfully deactivated Diagnosis: {} (ID: {}) in {} ms",
                    diagnosis.getCode(), diagnosis.getId(), endTime - startTime);

        } catch (ValidationException | NotFoundException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to deactivate Diagnosis with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.DIAGNOSIS_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public List<DiagnosisSummaryDTO> getAllSummaries() {
        log.debug("Fetching all active diagnosis summaries");
        long startTime = System.currentTimeMillis();

        try {
            List<Diagnosis> activeDiagnoses = diagnosisRepository.findByIsActiveTrueOrderByCode();

            long endTime = System.currentTimeMillis();
            log.debug("Retrieved {} active diagnoses in {} ms", activeDiagnoses.size(), endTime - startTime);

            return activeDiagnoses.stream()
                    .map(diagnosisMapper::toSummaryDTO)
                    .toList();
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to retrieve diagnosis summaries after {} ms: {}", endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.DIAGNOSIS_OPERATION_FAILED.getKey()), e
            );
        }
    }

}
