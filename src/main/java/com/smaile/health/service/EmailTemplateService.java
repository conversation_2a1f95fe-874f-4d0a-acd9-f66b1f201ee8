package com.smaile.health.service;

import java.util.Map;

public interface EmailTemplateService {

    /**
     * Process a template with the given data
     *
     * @param templateId   the template identifier
     * @param templateData the data to inject into the template
     * @return the processed template content
     */
    String processTemplate(String templateId, Map<String, Object> templateData);

    /**
     * Check if a template exists
     *
     * @param templateId the template identifier
     * @return true if template exists, false otherwise
     */
    boolean templateExists(String templateId);

    /**
     * Get available template IDs
     *
     * @return list of available template IDs
     */
    java.util.List<String> getAvailableTemplates();
}
