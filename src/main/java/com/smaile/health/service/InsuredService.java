package com.smaile.health.service;

import com.smaile.health.constants.InsuredStatusEnum;
import com.smaile.health.model.InsuredDTO;
import com.smaile.health.model.response.InsuredResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.UUID;

public interface InsuredService {
    UUID createInsured(InsuredDTO insuredDTO);

    UUID updateInsured(InsuredDTO dto);

    InsuredDTO getById(UUID id);

    Page<InsuredResponse> query(String keyword, InsuredStatusEnum status, Pageable pageable);

    UUID activeInsured(UUID id);

    UUID suspendInsured(UUID id, LocalDate effectiveDate, String reason);

    UUID cancelInsured(UUID id, LocalDate effectiveDate, String reason);
}
