package com.smaile.health.service;

import com.smaile.health.model.request.CreateTPARequestDTO;
import com.smaile.health.model.TPADTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface TPAService {

    Page<TPADTO> search(String country, String status, String name, String registrationNumber, String adminType, String icId, Pageable pageable);

    TPADTO get(UUID id);

    UUID create(CreateTPARequestDTO requestDTO);

    void update(UUID id, TPADTO tpaDTO);

    void activate(UUID id);

    void deactivate(UUID id);

    void delete(UUID id);

}
