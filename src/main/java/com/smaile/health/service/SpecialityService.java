package com.smaile.health.service;

import com.smaile.health.domain.Speciality;
import com.smaile.health.model.SpecialityDTO;
import com.smaile.health.model.request.CreateSpecialityRequestDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface SpecialityService {

    /**
     Search specialties with pagination and filters

     @param filter   Filter - searches in type or description fields (case-insensitive)
     @param pageable Pagination parameters
     @return Page of SpecialityDTO
     */
    Page<SpecialityDTO> search(String filter, Pageable pageable);

    /**
     Retrieves a specialty by ID

     @param id The specialty ID
     @return The specialty DTO
     @throws com.smaile.health.exception.ValidationException if not found
     */
    SpecialityDTO get(UUID id);

    /**
     Creates a new specialty

     @param requestDTO The specialty data to create
     @return The created specialty ID
     @throws com.smaile.health.exception.ValidationException if validation fails
     */
    UUID create(CreateSpecialityRequestDTO requestDTO);

    /**
     Updates an existing specialty

     @param id            The ID of the specialty to update
     @param specialityDTO The updated specialty data
     @throws com.smaile.health.exception.ValidationException if validation fails
     */
    void update(UUID id, SpecialityDTO specialityDTO);

    /**
     Deletes a specialty (soft delete by setting isActive to false)

     @param id The specialty ID to delete
     */
    void delete(UUID id);

    /**
     * Validates and returns all provided specialty IDs that exist and are active
     * @param specialtyIds List of specialty IDs to validate and retrieve
     * @return List of Speciality entities
     * @throws com.smaile.health.exception.ValidationException if any ID is not found or inactive
     */
    List<Speciality> getValidSpecialties(List<UUID> specialtyIds);
}
