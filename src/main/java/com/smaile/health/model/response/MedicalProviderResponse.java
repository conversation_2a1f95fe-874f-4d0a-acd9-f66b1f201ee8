package com.smaile.health.model.response;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.model.AgreementDTO;
import com.smaile.health.model.AgreementSimpleInfoDTO;
import com.smaile.health.model.MedicalProviderLicense;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.UUID;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MedicalProviderResponse {
    private UUID id;
    private String name;
    private OrganizationType type;
    private String providerType;
    private String market;
    private String country;
    private String registrationNumber;
    private OrganizationStatus status;
    private String contactPhone;
    private String contactEmail;
    private String address;
    private List<AgreementSimpleInfoDTO> agreements;
    private Integer agreementCount;
    private UUID parentId;
    private List<String> specialities;
    private List<MedicalProviderLicense> licenses;
}
