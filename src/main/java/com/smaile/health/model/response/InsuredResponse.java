package com.smaile.health.model.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.smaile.health.constants.InsuredGenderEnum;
import com.smaile.health.constants.InsuredStatusEnum;
import com.smaile.health.constants.LegalIdTypeEnum;
import com.smaile.health.model.InsuranceCompanyDTO;
import com.smaile.health.model.ModalityDTO;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InsuredResponse {
    private UUID id;
    private String modalityName;
    private ModalityDTO modality;
    private InsuranceCompanyDTO icOrg;
    private String insuredCode;
    private LocalDate firstEffectiveDate;
    private LocalDate coverageBeginDate;
    private LocalDate coverageEndDate;
    private InsuredStatusEnum status;
    private LocalDate statusDate;
    private String statusReason;
    private LegalIdTypeEnum legalIdType;
    private String legalIdValue;
    private InsuredGenderEnum gender;
    private LocalDate dateOfBirth;
    private String firstName;
    private String lastName;
    private Boolean isDeleted;
    private OffsetDateTime lastUpdated;
    private String updatedBy;
}
