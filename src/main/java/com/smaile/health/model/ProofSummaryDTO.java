package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Schema(description = "Proof Summary Data Transfer Object - contains only id and name")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProofSummaryDTO {

    private UUID id;

    @JsonProperty("code")
    private String code;
}
