package com.smaile.health.model;

import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.Status;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
@Builder
public class AboutMeDTO {

    @Schema(
            description = "User ID is provided platform",
            example = "abcd1234-5678-90ab-cdef-1234567890ab",
            maxLength = 100
    )
    private UUID userId;

    @Schema(
            description = "User fullName",
            example = "<PERSON> Smith",
            maxLength = 100
    )
    private String fullName;

    @Schema(
            description = "User email",
            example = "<EMAIL>",
            maxLength = 100
    )
    private String email;

    @Schema(
            description = "User phone",
            example = "**********",
            maxLength = 100
    )
    private String phone;

    @Schema(
            description = "User status",
            example = "ACTIVE",
            maxLength = 100
    )
    private Status status;

    @Schema(
            description = "Organization ID",
            example = "d6eb0f06-198f-4bf5-96d3-55d7ec384f33",
            maxLength = 100
    )
    private UUID organizationId;

    @Schema(
            description = "Organization type",
            example = "PROFESSIONAL",
            maxLength = 100
    )
    private OrganizationType organizationType;

    @Schema(
            description = "role of the user in the organization",
            example = "UUID",
            maxLength = 100
    )
    private String role;
}
