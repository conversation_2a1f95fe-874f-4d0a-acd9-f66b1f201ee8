package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Schema(description = "Diagnosis Summary Data Transfer Object - contains only id and code")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DiagnosisSummaryDTO {

    private UUID id;

    @JsonProperty("code")
    private String code;

    @JsonProperty("description")
    private String description;
}
