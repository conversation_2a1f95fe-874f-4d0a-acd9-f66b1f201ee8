package com.smaile.health.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.smaile.health.endpoint.validation.annotation.ValidDateRange;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

@Data
@ValidDateRange
public class CreateProofRequestDTO {

    @NotBlank
    private String code;

    private String description;

    @JsonProperty("effective_date")
    @NotNull
    private LocalDate effectiveDate;

    @JsonProperty("cancellation_date")
    private LocalDate cancellationDate;
}
