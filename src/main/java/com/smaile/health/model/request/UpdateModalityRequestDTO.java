package com.smaile.health.model.request;

import com.smaile.health.constants.NetworkType;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class UpdateModalityRequestDTO {

    @NotNull
    private String code;

    @NotNull
    private String name;

    @NotNull
    private String shortName;

    @NotNull
    private NetworkType networkType;

    private BigDecimal annualLimitGlobal;

    private BigDecimal annualLimitIn;

    private BigDecimal annualLimitOut;
}
