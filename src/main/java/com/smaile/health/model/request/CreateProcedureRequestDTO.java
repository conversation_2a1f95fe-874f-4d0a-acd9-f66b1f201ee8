package com.smaile.health.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.smaile.health.constants.AgeGroup;
import com.smaile.health.constants.DiagnosisFrequency;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "Request DTO for creating a new procedure")
public class CreateProcedureRequestDTO {

    @NotBlank
    private String code;

    @NotNull
    private DiagnosisFrequency frequency;

    @NotBlank
    private String description;

    @JsonProperty("age_group")
    @NotNull
    private AgeGroup ageGroup;

    @JsonProperty("market_cost")
    @NotNull
    private BigDecimal marketCost;

    @JsonProperty("mouth_part_requirements")
    private List<String> mouthPartRequirements;

    @JsonProperty("related_diagnosis_ids")
    private List<UUID> relatedDiagnosisIds;

    @JsonProperty("effective_date")
    @NotNull
    private LocalDate effectiveDate;

    @JsonProperty("cancellation_date")
    private LocalDate cancellationDate;

    @JsonProperty("specialty_ids")
    private List<UUID> specialtyIds;

    @JsonProperty("proof_ids")
    private List<UUID> proofIds;
}