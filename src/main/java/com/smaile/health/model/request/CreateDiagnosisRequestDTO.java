package com.smaile.health.model.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.smaile.health.constants.DiagnosisFrequency;
import com.smaile.health.constants.MouthPartType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
public class CreateDiagnosisRequestDTO {

    @NotBlank
    private String code;

    @NotNull
    private DiagnosisFrequency frequency;

    @NotBlank
    private String description;

    @JsonProperty("mouth_part_type")
    private MouthPartType mouthPartType;

    @JsonProperty("mouth_part_id")
    private String mouthPartId;

    @JsonProperty("market_cost")
    @NotNull
    private BigDecimal marketCost;

    @JsonProperty("knowledge_cost")
    @NotNull
    private BigDecimal knowledgeCost;

    @JsonProperty("effective_date")
    @NotNull
    private LocalDate effectiveDate;

    @JsonProperty("cancellation_date")
    private LocalDate cancellationDate;

    @JsonProperty("specialty_ids")
    private List<UUID> specialtyIds;

    @JsonProperty("proof_ids")
    private List<UUID> proofIds;
}
