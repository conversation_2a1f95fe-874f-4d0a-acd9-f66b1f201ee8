package com.smaile.health.model.request;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.UUID;

@Getter
@Setter
public class CreateInsuranceCompanyAgreementRequest {

    private UUID insuranceCompanyId;

    @NotNull(message = "Agreement Id is required")
    private UUID agreementId;

    @NotNull(message = "Effective time is required")
    private Instant effectiveTime;

    @NotNull(message = "Cancellation time is required")
    private Instant cancellationTime;

}
