package com.smaile.health.model.request;

import com.smaile.health.constants.InsuredGenderEnum;
import com.smaile.health.constants.LegalIdTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class UpdateInsuredRequest {

    private LocalDate firstEffectiveDate;

    private LocalDate coverageBeginDate;

    private LocalDate coverageEndDate;

    private LegalIdTypeEnum legalIdType;

    private String legalIdValue;

    private InsuredGenderEnum gender;

    private LocalDate dateOfBirth;

    private String firstName;

    private String lastName;
}
