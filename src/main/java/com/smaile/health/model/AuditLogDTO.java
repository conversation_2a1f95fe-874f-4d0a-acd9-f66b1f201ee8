package com.smaile.health.model;

import com.smaile.health.constants.Action;
import com.smaile.health.constants.EntityType;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.UUID;

@Getter
@Setter
@Builder
public class AuditLogDTO {

    private Instant timestampAction;
    private EntityType entity;
    private UUID entityId;
    private Action action;
    private String actor;
    private String description;

}
