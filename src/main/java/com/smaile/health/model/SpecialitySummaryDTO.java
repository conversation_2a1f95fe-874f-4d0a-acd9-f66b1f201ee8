package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Schema(description = "Speciality Summary Data Transfer Object - contains only id and code")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpecialitySummaryDTO {

    private UUID id;

    @JsonProperty("name")
    private String name;
}
