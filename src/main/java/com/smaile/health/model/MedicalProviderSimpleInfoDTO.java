package com.smaile.health.model;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import lombok.*;

import java.util.List;
import java.util.UUID;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MedicalProviderSimpleInfoDTO {
    private UUID id;
    private UUID parentId;
    private String name;
    private String code;
    private OrganizationType type;
    private String providerType;
    private String market;
    private String country;
    private String registrationNumber;
    private OrganizationStatus status;

    private String contactPhone;
    private String contactEmail;
    private String address;
}
