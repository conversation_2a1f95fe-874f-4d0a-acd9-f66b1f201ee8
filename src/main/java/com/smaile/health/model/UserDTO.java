package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.smaile.health.constants.Status;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.UUID;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDTO {

    private UUID id;

    private String keycloakId;

    private String username;

    private String email;

    private String fullName;

    private String phone;

    private Status status;

    private UUID organizationId;

    private OrganizationDTO organization;

    private String roleCode;

    private String roleDescription;

}
