package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 Base DTO class containing audit fields for entities
 */
@Data
public abstract class AuditDTO {

    @JsonProperty("date_created")
    private OffsetDateTime dateCreated;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("last_updated")
    private OffsetDateTime lastUpdated;

    @JsonProperty("updated_by")
    private String updatedBy;
}
