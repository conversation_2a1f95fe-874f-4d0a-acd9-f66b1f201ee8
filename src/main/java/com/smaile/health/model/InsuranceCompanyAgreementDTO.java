package com.smaile.health.model;

import com.smaile.health.constants.Status;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
public class InsuranceCompanyAgreementDTO {

    private UUID id;
    private AgreementDTO agreement;
    private InsuranceCompanyDTO insuranceCompany;
    private Instant effectiveTime;
    private Instant cancellationTime;
    private Status status;
}
