package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.smaile.health.constants.SmaileConstant;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.UUID;

@Getter
@Setter
public class RegisterProfessionalFormDTO {

    // Step 1
    @JsonProperty("full_professional_name")
    @Size(max = 255, message = "Full professional name must not exceed 255 characters")
    @NotNull
    private String fullProfessionalName;

    @JsonProperty("primary_license_id")
    @NotNull(message = "Primary license id is required")
    @Size(max = 255, message = "Primary License Id must not exceed 255 characters")
    private String primaryLicenseId;

    @JsonProperty("primary_practice_market")
    @Size(max = 255, message = "Primary License Id must not exceed 255 characters")
    @NotNull(message = "primary_practice_market is required")
    private String primaryPracticeMarket;

    @JsonProperty("country")
    @NotNull(message = "Country is required")
    @Size(max = 255, message = "Country must not exceed 255 characters")
    private String country;

    @JsonProperty("market_segment")
    @Size(max = 255, message = "Market Segment must not exceed 255 characters")
    @NotNull(message = "Market Segment is required")
    private String marketSegment;

    @JsonProperty("professional_specialties")
    @NotNull(message = "Professional Specialties is required")
    @Size(max = 255, message = "Professional Specialties must not exceed 255 characters")
    private List<String> professionalSpecialties;

    @JsonProperty("specialties")
    private List<UUID> specialties;

    @JsonProperty("professional_licenses")
    @NotNull(message = "Professional licenses are required")
    private List<ProfessionalLicenseDTO> professionalLicenses;

    // Step 2
    @JsonProperty("full_name")
    @NotNull(message = "Full name is required")
    @Size(max = 255, message = "Full name must not exceed 255 characters")
    private String fullName;

    @JsonProperty("username")
    @NotNull(message = "Username is required")
    @Size(max = 255, message = "username must not exceed 255 characters")
    private String username;

    @JsonProperty("email")
    @NotNull(message = "Email is required")
    @Pattern(regexp = SmaileConstant.EMAIL_REGEX, message = "Invalid email format")
    private String email;

    @JsonProperty("phone_number")
    @Size(max = 255, message = "Phone Number must not exceed 255 characters")
    private String phoneNumber;

    // Step 3: File uploads
    @JsonProperty("professional_license_file")
    private FileDTO professionalLicenseFile;

    @JsonProperty("professional_certificate_file")
    private FileDTO professionalCertificateFile;

    @JsonProperty("education_diploma_file")
    private FileDTO educationDiplomaFile;

    @JsonProperty("liability_insurance_file")
    private FileDTO liabilityInsuranceFile;

    @JsonProperty("photo_file")
    private FileDTO photoFile;

}
