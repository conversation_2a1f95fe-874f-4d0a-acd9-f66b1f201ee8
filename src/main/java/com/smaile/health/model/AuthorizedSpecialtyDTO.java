package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthorizedSpecialtyDTO {

    private UUID id;

    private String name;

    private String description;

    @JsonProperty("is_active")
    private Boolean isActive;

    @JsonProperty("date_created")
    private OffsetDateTime dateCreated;

    @JsonProperty("created_by")
    private String createdBy;

    @JsonProperty("last_updated")
    private OffsetDateTime lastUpdated;

    @JsonProperty("updated_by")
    private String updatedBy;
}
