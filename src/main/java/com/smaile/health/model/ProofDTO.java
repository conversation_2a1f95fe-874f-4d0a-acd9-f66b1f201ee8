package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.smaile.health.constants.Status;
import com.smaile.health.endpoint.validation.annotation.ValidDateRange;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.UUID;

@Data
@Schema(description = "Proof Data Transfer Object")
@ValidDateRange
public class ProofDTO extends AuditDTO {

    private UUID id;

    @NotBlank
    private String code;

    private String description;

    @JsonProperty("effective_date")
    @NotNull
    private LocalDate effectiveDate;

    @JsonProperty("cancellation_date")
    private LocalDate cancellationDate;

    @JsonProperty("is_active")
    private Boolean isActive;

    @JsonProperty("status")
    private Status status;
}
