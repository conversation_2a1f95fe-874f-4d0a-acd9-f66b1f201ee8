package com.smaile.health.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailResponse {

    private String messageId;
    private String status;
    private LocalDateTime sentAt;
    private List<String> recipients;
    private String subject;
    private String errorMessage;
    private boolean success;

    public static EmailResponse success(String messageId, List<String> recipients, String subject) {
        return EmailResponse.builder()
                .messageId(messageId)
                .status("SENT")
                .sentAt(LocalDateTime.now())
                .recipients(recipients)
                .subject(subject)
                .success(true)
                .build();
    }

    public static EmailResponse failure(String errorMessage, List<String> recipients, String subject) {
        return EmailResponse.builder()
                .status("FAILED")
                .sentAt(LocalDateTime.now())
                .recipients(recipients)
                .subject(subject)
                .errorMessage(errorMessage)
                .success(false)
                .build();
    }
}
