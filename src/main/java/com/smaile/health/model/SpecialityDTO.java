package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Schema(description = "Speciality Data Transfer Object")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpecialityDTO extends AuditDTO {

    private UUID id;

    @NotBlank
    private String name;

    private String description;

    @JsonProperty("is_active")
    private Boolean isActive;
}
