package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
public class ProfessionalLicenseDTO {

    @JsonProperty("license_type")
    @NotNull(message = "License type is required")
    private String licenseType;

    @JsonProperty("license_number")
    @NotNull(message = "License number is required")
    private String licenseNumber;

    @JsonProperty("expiration_date")
    @NotNull(message = "Expiration date is required")
    private String expirationDate;

    @JsonProperty("issuing_authority")
    @NotNull(message = "Issuing authority is required")
    private String issuingAuthority;
}
