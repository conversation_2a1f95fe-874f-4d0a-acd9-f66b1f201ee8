package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "Proof Summary Response Data Transfer Object")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProofSummaryResponseDTO {

    @JsonProperty("total_proofs")
    @Schema(description = "Total number of proofs")
    private Long totalProofs;

    @JsonProperty("active_proofs")
    @Schema(description = "Number of active proofs")
    private Long activeProofs;

    @JsonProperty("inactive_proofs")
    @Schema(description = "Number of inactive proofs")
    private Long inactiveProofs;

    @JsonProperty("expired_proofs")
    @Schema(description = "Number of expired proofs")
    private Long expiredProofs;
}
