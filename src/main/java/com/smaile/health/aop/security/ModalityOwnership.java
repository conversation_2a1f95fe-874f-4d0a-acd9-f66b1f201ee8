package com.smaile.health.aop.security;

import com.smaile.health.repository.ModalityRepository;
import com.smaile.health.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.UUID;


@Component("modalityOwnership")
@RequiredArgsConstructor
public class ModalityOwnership {

    private final ModalityRepository modalityRepository;

    public boolean owns(UUID modalityId) {

        return modalityRepository.existsByIdAndIcOrg_Id(modalityId, SecurityUtils.getActorOrgId());
    }
}
