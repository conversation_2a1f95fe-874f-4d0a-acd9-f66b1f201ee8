package com.smaile.health.aop;

import com.smaile.health.config.CustomAuthentication;
import com.smaile.health.constants.Constants;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.domain.Organization;
import com.smaile.health.repository.OrganizationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Set;
import java.util.UUID;

@Component("permissionEvaluator")
@Slf4j
public class CustomPermissionEvaluator implements PermissionEvaluator {

    @Autowired
    private OrganizationRepository organizationRepository;
    @Override
    public boolean hasPermission(Authentication auth, Object targetDomainObject, Object permission) {
        CustomAuthentication authentication = (CustomAuthentication) auth;
        // Super admin has authority on all actions at PoC phase
        if (authentication.getRoles().contains(RoleEnum.SUPER_SMAILE_ADMIN.name()) ||
                authentication.getRoles().contains(Constants.ROLE_PREFIX + RoleEnum.SUPER_SMAILE_ADMIN.name()))
            return true;

        // All non-super admin require orgId param
        if (targetDomainObject == null) {
            throw new IllegalStateException("Missing parameter for permission check");
        }
        UUID orgId = (UUID) targetDomainObject;

        // Check if user has direct permission on the target organization
        if (authentication.getOrganizationToPermissionsMap()
                .getOrDefault(orgId, Set.of())
                .contains(String.valueOf(permission))) {
            return true;
        }

        // Check if user has permission on the parent organization (for child org access)
        UUID parentOrgId = authentication.getActor().getOrganization().getId();
        if (parentOrgId != null && !parentOrgId.equals(orgId)) {
            // Check if the target org is a child of the user's organization
            if (isChildOrganization(orgId, parentOrgId)) {
                // Check if user has the corresponding permission on their parent organization
                if (authentication.getOrganizationToPermissionsMap()
                        .getOrDefault(parentOrgId, Set.of())
                        .contains(String.valueOf(permission))) {
                    return true;
                }
            }
        }

        log.debug("User {} is missing permission {} on orgId {}", authentication.getActor().getEmail(), permission, orgId);
        return false;
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        return false;
    }

    /**
     * Checks if the target organization is a child of the parent organization
     *
     * @param targetOrgId the ID of the target organization to check
     * @param parentOrgId the ID of the parent organization
     * @return true if target is a child of parent, false otherwise
     */
    private boolean isChildOrganization(UUID targetOrgId, UUID parentOrgId) {
        try {
            Organization targetOrg = organizationRepository.findById(targetOrgId).orElse(null);
            if (targetOrg == null) {
                log.debug("Target organization {} not found", targetOrgId);
                return false;
            }

            // Check if the target org's parent is the specified parent org
            if (targetOrg.getParent() != null && parentOrgId.equals(targetOrg.getParent().getId())) {
                log.debug("Organization {} is a direct child of parent organization {}", targetOrgId, parentOrgId);
                return true;
            }

            log.debug("Organization {} is not a child of parent organization {}", targetOrgId, parentOrgId);
            return false;
        } catch (Exception e) {
            log.error("Error checking if organization {} is child of {}: {}", targetOrgId, parentOrgId, e.getMessage(), e);
            return false;
        }
    }
}
