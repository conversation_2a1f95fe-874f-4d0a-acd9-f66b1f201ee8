package com.smaile.health.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.model.ProfessionalDTO;
import com.smaile.health.model.ProfessionalSummaryDTO;
import com.smaile.health.model.request.Filter;
import com.smaile.health.model.request.ProfessionalBulkLinkRequest;
import com.smaile.health.model.response.BaseResponse;
import com.smaile.health.model.response.ProfessionalLinkingResponse;
import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.service.ProfessionalService;
import com.smaile.health.util.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(value = "/professionals", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Professional Controller", description = "Professional management APIs")
public class ProfessionalController {

    private final ProfessionalService professionalService;
    private final ObjectMapper objectMapper;

    @GetMapping
    @Operation(summary = "Get list of Professional ")
    public ResponseEntity<PageResponse<ProfessionalDTO>> getAllProfessionalsPaged(
            @Schema(
                    description = "Type of insurance business (e.g. IC, TPA)",
                    example = "Mike Smith",
                    maxLength = 100
            )
            @RequestParam(required = false, name = "search") String search,
            @Schema(
                    description = "Filter query in JSON format",
                    example = """
                            [
                              {
                                "field":"status",
                                "operator":"EQ",
                                "value":"ACTIVE"}
                            ]
                            """,
                    maxLength = 100
            )
            @RequestParam(required = false, name = "filters") String filterQuery,
            @PageableDefault(size = 20, sort = "dateCreated", direction = Sort.Direction.DESC) Pageable pageable) throws JsonProcessingException {
        List<Filter> filters = new ArrayList<>();
        if (StringUtils.hasText(filterQuery)) {
            filters = objectMapper.readValue(filterQuery, objectMapper.getTypeFactory().constructCollectionType(List.class, Filter.class));
        }
        return ResponseEntity.ok(professionalService.query(search, filters, pageable));
    }

    @GetMapping("/summary")
    @Operation(summary = "Get list of user by organization id")
    public ResponseEntity<BaseResponse<ProfessionalSummaryDTO>> summary() {
        return ResponseEntity.ok(BaseResponse.of(professionalService.summary()));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get professional details")
    public ResponseEntity<BaseResponse<ProfessionalDTO>> detail(@PathVariable("id") UUID professionalId) {
        return ResponseEntity.ok(BaseResponse.of(professionalService.detail(professionalId)));
    }

    @PostMapping("/{id}/link")
    @Operation(summary = "Link professional to actor's organization")
    public ResponseEntity<SmaileApiResponse<UUID>> link(@PathVariable("id") UUID professionalId) {
        UUID linkId = professionalService.linkToActorOrg(professionalId);
        return ResponseEntity.ok(SmaileApiResponse.success(linkId));
    }

    @PostMapping("/link")
    @Operation(summary = "Link a list of professional to actor's organization")
    public ResponseEntity<Void> link(@RequestBody @Valid ProfessionalBulkLinkRequest request) {
        professionalService.bulkLinkToActorOrg(request);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/unlink")
    @Operation(summary = "Unlink professional from actor's organization")
    public ResponseEntity<SmaileApiResponse<UUID>> unlink(@PathVariable("id") UUID professionalId) {
        UUID relationId = professionalService.unlinkFromActorOrg(professionalId);
        return ResponseEntity.ok(SmaileApiResponse.success(relationId));
    }

    @GetMapping("/query")
    @Operation(summary = "Query professional for linking/unlinking")
    public ResponseEntity<PageResponse<ProfessionalLinkingResponse>> query(
            @RequestParam(required = false, name = "linked") Boolean linked,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String country,
            @RequestParam(required = false) String speciality,
            @PageableDefault(sort = "lastUpdated", direction = Sort.Direction.DESC) Pageable pageable
    ) {
        Page<ProfessionalLinkingResponse> response = professionalService.queryForLinking(linked, keyword, country, speciality, pageable);
        return ResponseEntity.ok(PageResponse.of(response));
    }

    @PutMapping("/{id}/approve")
    @Operation(summary = "Approve an professional")
    public ResponseEntity<BaseResponse<ProfessionalDTO>> approve(@PathVariable("id") UUID professionalId) {
        return ResponseEntity.ok(BaseResponse.of(professionalService.approve(professionalId)));
    }

    @PutMapping("/{id}/deny")
    @Operation(summary = "Deny an professional")
    public ResponseEntity<BaseResponse<ProfessionalDTO>> deny(@PathVariable("id") UUID professionalId) {
        return ResponseEntity.ok(BaseResponse.of(professionalService.deny(professionalId)));
    }

    @PutMapping("/{id}/activate")
    @Operation(summary = "Activate professional", description = "Activate a professional by setting status to ACTIVE")
    public ResponseEntity<BaseResponse<String>> activateProfessional(@PathVariable("id") UUID professionalId) {
        professionalService.activate(professionalId);
        return ResponseEntity.ok(new BaseResponse<>("Professional activated successfully"));
    }

    @PutMapping("/{id}/deactivate")
    @Operation(summary = "Deactivate professional", description = "Deactivate a professional by setting status to INACTIVE")
    public ResponseEntity<BaseResponse<String>> deactivateProfessional(@PathVariable("id") UUID professionalId) {
        professionalService.deactivate(professionalId);
        return ResponseEntity.ok(new BaseResponse<>("Professional deactivated successfully"));
    }
}
