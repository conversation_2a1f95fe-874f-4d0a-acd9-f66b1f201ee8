package com.smaile.health.controller;

import com.smaile.health.constants.MessageKey;
import com.smaile.health.model.ProcedureDTO;
import com.smaile.health.model.request.CreateProcedureRequestDTO;
import com.smaile.health.model.request.UpdateProcedureRequestDTO;
import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ProcedureService;
import com.smaile.health.util.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(value = "/procedures", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Procedure", description = "Procedure management APIs")
public class ProcedureController {

    private final ProcedureService procedureService;
    private final I18nService i18nService;

    @GetMapping("/query")
    @Operation(summary = "Search procedures", description = "Search procedures with pagination and filters")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Procedures retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Invalid parameters or validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<PageResponse<ProcedureDTO>>> searchProcedures(
            @Parameter(description = "Filter - searches in code and description fields (case-insensitive)", example = "D1234")
            @RequestParam(required = false) String filter,
            @PageableDefault(size = 20) Pageable pageable) {

        log.debug("Searching procedures with filter - Filter: {}", filter);

        PageResponse<ProcedureDTO> pageResponse = PageResponse.of(procedureService.search(filter, pageable));

        String message = i18nService.getMessage(MessageKey.PROCEDURE_RETRIEVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(pageResponse, message));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get procedure by ID", description = "Retrieve a specific procedure by its ID")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Procedure retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Bad request - Invalid ID format"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Not found - Procedure not found"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<ProcedureDTO>> getProcedureById(
            @Parameter(description = "Unique identifier of the procedure", example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable UUID id) {
        log.debug("Fetching procedure with ID: {}", id);

        ProcedureDTO procedure = procedureService.getProcedureById(id);
        String message = i18nService.getMessage(MessageKey.PROCEDURE_RETRIEVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(procedure, message));
    }

    @PostMapping
    @Operation(summary = "Create procedure", description = "Create a new procedure")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Procedure created successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(responseCode = "409", description = "Conflict - Procedure with same code already exists"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<ProcedureDTO>> createProcedure(
            @Parameter(
                    description = "Procedure data to create",
                    required = true,
                    content = @Content(schema = @Schema(implementation = CreateProcedureRequestDTO.class))
            )
            @Valid @RequestBody CreateProcedureRequestDTO requestDTO) {
        log.debug("Creating new procedure: {}", requestDTO.getCode());

        ProcedureDTO createdProcedure = procedureService.createProcedure(requestDTO);
        String message = i18nService.getMessage(MessageKey.PROCEDURE_CREATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(createdProcedure, message));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update procedure", description = "Update an existing procedure")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Procedure updated successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Not found - Procedure not found"),
            @ApiResponse(responseCode = "409", description = "Conflict - Procedure with same code already exists"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<ProcedureDTO>> updateProcedure(
            @Parameter(description = "Unique identifier of the procedure to update", example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable UUID id,
            @Parameter(
                    description = "Updated procedure data",
                    required = true,
                    content = @Content(schema = @Schema(implementation = UpdateProcedureRequestDTO.class))
            )
            @Valid @RequestBody UpdateProcedureRequestDTO requestDTO) {
        log.debug("Updating procedure with ID: {}", id);

        ProcedureDTO updatedProcedure = procedureService.updateProcedure(id, requestDTO);
        String message = i18nService.getMessage(MessageKey.PROCEDURE_UPDATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(updatedProcedure, message));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Deactivate procedure", description = "Deactivate a procedure (soft delete)")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Procedure deactivated successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Bad request - Invalid ID format"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Not found - Procedure not found"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<String>> deleteProcedure(
            @Parameter(description = "Unique identifier of the procedure to deactivate", example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable UUID id) {
        log.debug("Deactivating procedure with ID: {}", id);

        procedureService.deleteProcedure(id);
        String message = i18nService.getMessage(MessageKey.PROCEDURE_DEACTIVATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(id.toString(), message));
    }

}
