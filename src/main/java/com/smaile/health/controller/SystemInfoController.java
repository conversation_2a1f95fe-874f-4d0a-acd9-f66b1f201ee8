package com.smaile.health.controller;

import com.smaile.health.config.SystemInfoConfig;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/system-info")
@ApiResponse(description = "System info management APIs")
@RequiredArgsConstructor
public class SystemInfoController {
    private final SystemInfoConfig infoConfig;

    @GetMapping("/configuration")
    @Operation(summary = "Retrieve system configuration")
    public ResponseEntity<SystemInfoConfig> getSystemConfiguration() {
        return ResponseEntity.ok(infoConfig);
    }
}
