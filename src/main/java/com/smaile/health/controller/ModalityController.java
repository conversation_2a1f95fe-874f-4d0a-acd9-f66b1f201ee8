package com.smaile.health.controller;

import com.smaile.health.constants.MessageKey;
import com.smaile.health.constants.NetworkType;
import com.smaile.health.model.ModalityDTO;
import com.smaile.health.model.ModalitySearchCriteria;
import com.smaile.health.model.request.CreateModalityRequestDTO;
import com.smaile.health.model.request.UpdateModalityRequestDTO;
import com.smaile.health.model.response.DetailModalityResponse;
import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ModalityService;
import com.smaile.health.util.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping(value = "/modalities", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Modality Controller", description = "Modality management APIs")
public class ModalityController {

    private final ModalityService modalityService;

    private final I18nService i18nService;

    @PostMapping
    @Operation(summary = "Create modality", description = "Create a new modality")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Modality created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data"),
            @ApiResponse(responseCode = "403", description = "Forbidden - IC_ADMIN role required"),
    })
    public ResponseEntity<SmaileApiResponse<UUID>> createModality(
            @Parameter(
                    description = "Modality data to create",
                    required = true,
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = CreateModalityRequestDTO.class)
                    )
            )
            @RequestBody @Valid final CreateModalityRequestDTO requestDTO) {
        final UUID createdId = modalityService.create(requestDTO);
        String message = i18nService.getMessage(MessageKey.MODALITY_CREATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(createdId, message));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update modality", description = "Update an existing modality")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Modality updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data"),
            @ApiResponse(responseCode = "403", description = "Forbidden - IC_ADMIN role required"),
    })
    public ResponseEntity<SmaileApiResponse<UUID>> updateModality(
            @Parameter(description = "Modality ID", required = true)
            @PathVariable final UUID id,
            @Parameter(
                    description = "Updated specialty data",
                    required = true,
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = UpdateModalityRequestDTO.class)
                    )
            )
            @RequestBody @Valid final UpdateModalityRequestDTO requestDTO) {
        modalityService.update(id, requestDTO);
        String message = i18nService.getMessage(MessageKey.MODALITY_UPDATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(null, message));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get modality by ID", description = "Get details of a modality")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Modality retrieved successfully"),
            @ApiResponse(responseCode = "403", description = "Forbidden - IC_ADMIN role required"),
    })
    public ResponseEntity<SmaileApiResponse<DetailModalityResponse>> getModality(
            @Parameter(description = "Modality ID", required = true)
            @PathVariable final UUID id) {
        DetailModalityResponse modalityResponse = modalityService.detail(id);
        String message = i18nService.getMessage(MessageKey.MODALITY_RETRIEVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(modalityResponse, message));
    }

    @GetMapping("/search")
    @Operation(summary = "Get list modalities", description = "Get list modalities with search, filter options")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Modality retrieved successfully"),
            @ApiResponse(responseCode = "403", description = "Forbidden - IC_ADMIN role required")
    })
    public ResponseEntity<SmaileApiResponse<PageResponse<ModalityDTO>>> query(
            @Schema(
                    description = "Text search by modality id, name and short name",
                    example = "Dental Basic",
                    maxLength = 255
            )
            @RequestParam(required = false, name = "search") String keyword,
            @RequestParam(required = false, name = "network_type") NetworkType networkType,
            @PageableDefault(page = 0, size = 10, sort = "dateCreated", direction = Sort.Direction.DESC) Pageable pageable
    ) {
        ModalitySearchCriteria criteria = ModalitySearchCriteria.builder()
                .networkType(networkType)
                .search(keyword)
                .build();

        Page<ModalityDTO> modalities = modalityService.query(criteria, pageable);

        String message = i18nService.getMessage(MessageKey.MODALITY_RETRIEVED.getKey());

        return ResponseEntity.ok(SmaileApiResponse.success(PageResponse.of(modalities), message));
    }

}
