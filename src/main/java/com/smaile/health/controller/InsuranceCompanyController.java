package com.smaile.health.controller;

import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.constants.MessageKey;
import com.smaile.health.model.request.CreateInsuranceCompanyRequestDTO;

import com.smaile.health.model.InsuranceCompanyDTO;
import com.smaile.health.model.InsuranceCompanyGeneralInfoDTO;
import com.smaile.health.service.InsuranceCompanyService;
import com.smaile.health.service.I18nService;

import com.smaile.health.util.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;
import java.util.List;

@RestController
@RequestMapping(value = "/insurance-companies", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Insurance Company", description = "Insurance Company management APIs")
public class InsuranceCompanyController {

    private final InsuranceCompanyService insuranceCompanyService;
    private final I18nService i18nService;

    @GetMapping("/query")
    @Operation(summary = "Search insurance companies", description = "Search insurance companies with pagination and filters")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Insurance companies retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Invalid parameters or validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<PageResponse<InsuranceCompanyDTO>>> searchInsuranceCompanies(
            @Parameter(description = "Market filter (e.g., 'US', 'EU', 'ASIA')", example = "US")
            @RequestParam(required = false) String market,
            @Parameter(description = "Status filter - ACTIVE or INACTIVE", example = "ACTIVE")
            @RequestParam(required = false) String status,
            @Parameter(description = "Name filter - partial match (case-insensitive)", example = "ABC Insurance")
            @RequestParam(required = false) String name,

            @Parameter(description = "Pagination parameters (page, size, sort)")
            @PageableDefault(size = 20, sort = "id") Pageable pageable) {
        PageResponse<InsuranceCompanyDTO> pageResponse = PageResponse.of(insuranceCompanyService.search(market, status, name, pageable));
        String message = i18nService.getMessage(MessageKey.INSURANCE_COMPANY_RETRIEVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(pageResponse, message));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get insurance company by ID", description = "Retrieve a specific insurance company by its ID")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Insurance company retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Not found - Insurance company not found"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<InsuranceCompanyDTO>> getInsuranceCompany(
            @Parameter(description = "Unique identifier of the insurance company", example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable(name = "id") final UUID id) {
        InsuranceCompanyDTO insuranceCompany = insuranceCompanyService.get(id);
        String message = i18nService.getMessage(MessageKey.INSURANCE_COMPANY_RETRIEVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(insuranceCompany, message));
    }

    @GetMapping("/general-info")
    @Operation(summary = "Get general info of active insurance companies list", description = "Retrieve a list of all active insurance companies with basic information (ID and name)")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "General info of active insurance companies list retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<List<InsuranceCompanyGeneralInfoDTO>>> getGeneralInfoActiveInsuranceCompanies() {
        List<InsuranceCompanyGeneralInfoDTO> activeInsuranceCompanies = insuranceCompanyService.getActiveInsuranceCompanies();
        String message = i18nService.getMessage(MessageKey.INSURANCE_COMPANY_RETRIEVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(activeInsuranceCompanies, message));
    }

    @PostMapping
    @Operation(summary = "Create insurance company", description = "Create a new insurance company. Admin user information is required and the admin user will automatically receive an invitation email with temporary password.")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Insurance company created successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions (SUPER_SMAILE_ADMIN required)"),
            @ApiResponse(responseCode = "409", description = "Conflict - Insurance company with same name already exists"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    //    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public ResponseEntity<SmaileApiResponse<UUID>> createInsuranceCompany(
            @Parameter(
                    description = "Insurance company data to create",
                    required = true,
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = CreateInsuranceCompanyRequestDTO.class)
                    )
            )
            @RequestBody @Valid final CreateInsuranceCompanyRequestDTO requestDTO) {
        final UUID createdId = insuranceCompanyService.create(requestDTO);
        String message = i18nService.getMessage(MessageKey.INSURANCE_COMPANY_CREATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(createdId, message));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update insurance company", description = "Update an existing insurance company")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Insurance company updated successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions (SUPER_SMAILE_ADMIN required)"),
            @ApiResponse(responseCode = "404", description = "Not found - Insurance company not found"),
            @ApiResponse(responseCode = "409", description = "Conflict - Insurance company with same name already exists"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    //    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    public ResponseEntity<SmaileApiResponse<String>> updateInsuranceCompany(
            @Parameter(description = "Unique identifier of the insurance company to update", example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable(name = "id") final UUID id,
            @Parameter(
                    description = "Updated insurance company data",
                    required = true,
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = InsuranceCompanyDTO.class)
                    )
            )
            @RequestBody @Valid final InsuranceCompanyDTO insuranceCompanyDTO) {
        insuranceCompanyService.update(id, insuranceCompanyDTO);
        String message = i18nService.getMessage(MessageKey.INSURANCE_COMPANY_UPDATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(id.toString(), message));
    }

    @PutMapping("/{id}/activate")
    @Operation(summary = "Activate insurance company", description = "Activate an insurance company by setting status to ACTIVE")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Insurance company activated successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Bad request - Invalid ID format or already active"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Not found - Insurance company not found"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<String>> activateInsuranceCompany(
            @Parameter(description = "Unique identifier of the insurance company to activate", example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable(name = "id") final UUID id) {
        insuranceCompanyService.activate(id);
        String message = i18nService.getMessage(MessageKey.INSURANCE_COMPANY_ACTIVATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(id.toString(), message));
    }

    @PutMapping("/{id}/deactivate")
    @Operation(summary = "Deactivate insurance company", description = "Deactivate an insurance company by setting status to INACTIVE")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Insurance company deactivated successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Bad request - Invalid ID format or already inactive"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Not found - Insurance company not found"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<String>> deactivateInsuranceCompany(
            @Parameter(description = "Unique identifier of the insurance company to deactivate", example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable(name = "id") final UUID id) {
        insuranceCompanyService.deactivate(id);
        String message = i18nService.getMessage(MessageKey.INSURANCE_COMPANY_DEACTIVATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(id.toString(), message));
    }


}
