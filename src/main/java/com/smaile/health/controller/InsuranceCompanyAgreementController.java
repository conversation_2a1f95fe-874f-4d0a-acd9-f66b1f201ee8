package com.smaile.health.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.constants.EntityType;
import com.smaile.health.model.AuditLogDTO;
import com.smaile.health.model.InsuranceCompanyAgreementDTO;
import com.smaile.health.model.request.*;
import com.smaile.health.model.response.BaseResponse;
import com.smaile.health.service.InsuranceCompanyAgreementService;
import com.smaile.health.util.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(value = "/insurance-company-agreements", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Insurance Company Agreements Controller", description = "Insurance company agreements management APIs")
public class InsuranceCompanyAgreementController {

    private final InsuranceCompanyAgreementService insuranceCompanyAgreementService;

    private final ObjectMapper objectMapper;

    @GetMapping
//    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #id, 'agreements:*:read')")
    public ResponseEntity<PageResponse<InsuranceCompanyAgreementDTO>> getPage(
            @Schema(
                    description = "Text search by long description, short description and contract id",
                    example = "CCNSxzz",
                    maxLength = 100
            )
            @RequestParam(required = false, name = "search") String search,
            @Schema(
                    description = "Filter query in JSON format. This api support fields: status, provider.id",
                    example = """
                            [
                              {
                                "field":"status",
                                "operator":"EQ",
                                "value":"ACTIVE"
                              }
                            ]
                            """,
                    maxLength = 100
            )
            @RequestParam(required = false, name = "filters") String filterQuery,
            @PageableDefault(size = 20, sort = "dateCreated", direction = Sort.Direction.DESC) Pageable pageable) throws JsonProcessingException {
        List<Filter> filters = new ArrayList<>();
        if (StringUtils.hasText(filterQuery)) {
            filters = objectMapper.readValue(filterQuery, objectMapper.getTypeFactory().constructCollectionType(List.class, Filter.class));
        }
        return ResponseEntity.ok(insuranceCompanyAgreementService.query(search, filters, pageable));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get details")
//    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #id, 'agreements:*:read')")
    public ResponseEntity<BaseResponse<InsuranceCompanyAgreementDTO>> detail(@PathVariable("id") UUID id) {
        return ResponseEntity.ok(BaseResponse.of(insuranceCompanyAgreementService.detail(id)));
    }

    @GetMapping("/{id}/audit-logs")
    public ResponseEntity<PageResponse<AuditLogDTO>> getAuditLog(
            @PathVariable("id") String id,
            @Schema(
                    description = "Text search by description",
                    example = "CCNSxzz",
                    maxLength = 100
            )
            @RequestParam(required = false, name = "search") String search,
            @Schema(
                    description = "Filter query in JSON format. This api support fields: entityId, actor",
                    example = """
                            [
                              {
                                "field":"status",
                                "operator":"EQ",
                                "value":"ACTIVE"
                              }
                            ]
                            """,
                    maxLength = 100
            )
            @RequestParam(required = false, name = "filters") String filterQuery,
            @PageableDefault(page = 0, size = 20, sort = "dateCreated", direction = Sort.Direction.DESC)
            Pageable pageable) throws JsonProcessingException {
        List<Filter> filters = new ArrayList<>();
        if (StringUtils.hasText(filterQuery)) {
            filters = objectMapper.readValue(filterQuery, objectMapper.getTypeFactory().constructCollectionType(List.class, Filter.class));
        }
        return ResponseEntity.ok().build();
    }

    @PostMapping
    @Operation(summary = "Create Ic Agreement")
//    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #agreement.providerId, 'agreements:*:create')")
    public ResponseEntity<BaseResponse<InsuranceCompanyAgreementDTO>> create(@RequestBody CreateInsuranceCompanyAgreementRequest request) {
        return ResponseEntity.ok(BaseResponse.of(insuranceCompanyAgreementService.create(request)));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update Ic Agreement")
//    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #agreement.providerId, 'agreements:*:update')")
    public ResponseEntity<BaseResponse<InsuranceCompanyAgreementDTO>> update(@PathVariable("id") UUID id, @RequestBody UpdateInsuranceCompanyAgreementRequest request) {
        request.setId(id);
        return ResponseEntity.ok(BaseResponse.of(insuranceCompanyAgreementService.update(request)));
    }

}
