package com.smaile.health.controller;

import com.smaile.health.constants.MessageKey;
import com.smaile.health.constants.Status;
import com.smaile.health.model.ProofDTO;
import com.smaile.health.model.ProofSummaryDTO;
import com.smaile.health.model.ProofSummaryResponseDTO;
import com.smaile.health.model.request.CreateProofRequestDTO;
import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ProofService;
import com.smaile.health.util.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(value = "/proofs", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Proof", description = "Proof management APIs")
public class ProofController {

    private final ProofService proofService;
    private final I18nService i18nService;

    @GetMapping("/query")
    @Operation(summary = "Search proofs", description = "Search proofs with pagination and filters")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Proofs retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Invalid parameters or validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<PageResponse<ProofDTO>>> searchProofs(
            @Parameter(description = "Filter - searches in code or description fields (case-insensitive)", example = "X-RAY")
            @RequestParam(required = false) String filter,
            @Parameter(description = "Status filter - ACTIVE, INACTIVE, or EXPIRED", example = "ACTIVE")
            @RequestParam(required = false) Status status,
            @Parameter(description = "Pagination parameters (page, size, sort)")
            @PageableDefault(size = 20, sort = "lastUpdated", direction = org.springframework.data.domain.Sort.Direction.DESC) Pageable pageable) {
        PageResponse<ProofDTO> pageResponse;
        if (status != null) {
            pageResponse = PageResponse.of(proofService.search(filter, status, pageable));
        } else {
            pageResponse = PageResponse.of(proofService.search(filter, pageable));
        }
        String message = i18nService.getMessage(MessageKey.PROOF_RETRIEVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(pageResponse, message));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get proof by ID", description = "Retrieve a specific proof by its ID")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Proof retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "Proof not found",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<ProofDTO>> getProof(
            @Parameter(description = "Proof ID", required = true,
                    example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable(name = "id") final UUID id) {

        ProofDTO proof = proofService.get(id);
        String message = i18nService.getMessage(MessageKey.PROOF_RETRIEVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(proof, message));
    }

    @PostMapping
    @Operation(summary = "Create proof", description = "Create a new proof")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Proof created successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(responseCode = "409", description = "Conflict - Proof with same name already exists"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<UUID>> createProof(
            @Parameter(description = "Proof creation request", required = true)
            @Valid @RequestBody CreateProofRequestDTO requestDTO) {

        UUID proofId = proofService.create(requestDTO);
        String message = i18nService.getMessage(MessageKey.PROOF_CREATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(proofId, message));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update proof", description = "Update an existing proof")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Proof updated successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "Proof not found",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(responseCode = "409", description = "Conflict - Proof with same name already exists"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<UUID>> updateProof(
            @Parameter(description = "Proof ID", required = true,
                    example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable(name = "id") final UUID id,
            @Parameter(description = "Proof update request", required = true)
            @Valid @RequestBody ProofDTO proofDTO) {

        proofService.update(id, proofDTO);
        String message = i18nService.getMessage(MessageKey.PROOF_UPDATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(id, message));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete proof", description = "Delete (deactivate) a proof")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Proof deleted successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "Proof not found",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<UUID>> deleteProof(
            @Parameter(description = "Proof ID", required = true,
                    example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable(name = "id") final UUID id) {

        proofService.delete(id);
        String message = i18nService.getMessage(MessageKey.PROOF_DELETED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(id, message));
    }

    @GetMapping("/active")
    @Operation(summary = "Get active proofs", description = "Retrieve all active proofs as summary DTOs")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Active proofs retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<List<ProofSummaryDTO>>> getActiveProofs() {
        List<ProofSummaryDTO> activeProofs = proofService.getActiveProofs();
        String message = i18nService.getMessage(MessageKey.PROOF_RETRIEVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(activeProofs, message));
    }

    @GetMapping("/summary")
    @Operation(summary = "Get proof summary statistics", description = "Retrieve proof summary statistics including total count and count by status")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Proof summary retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<ProofSummaryResponseDTO>> getProofSummary() {
        ProofSummaryResponseDTO summary = proofService.getSummary();
        String message = i18nService.getMessage(MessageKey.PROOF_RETRIEVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(summary, message));
    }
}
