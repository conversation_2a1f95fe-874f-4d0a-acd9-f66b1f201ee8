package com.smaile.health.repository.entitymanager;

import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * Finding ID fields in entity classes
 */
public class EntityIdFieldFinder {

    /**
     * Main method to find the ID field for different ID strategies
     */
    public static Field findIdField(Class<?> entityClass) {
        Field idField = findSimpleIdField(entityClass);
        if (idField != null) {
            return idField;
        }

        Field embeddedIdField = findEmbeddedIdField(entityClass);
        if (embeddedIdField != null) {
            return embeddedIdField;
        }

        return findCompositeIdField(entityClass);
    }

    /**
     * Single field annotated with @Id
     */
    private static Field findSimpleIdField(Class<?> entityClass) {
        Class<?> currentClass = entityClass;

        while (currentClass != null && currentClass != Object.class) {
            Field[] fields = currentClass.getDeclaredFields();

            for (Field field : fields) {
                if (field.isAnnotationPresent(Id.class)) {
                    field.setAccessible(true);
                    return field;
                }
            }
            currentClass = currentClass.getSuperclass();
        }
        return null;
    }

    /**
     * Embedded ID - Single field annotated with @EmbeddedId
     */
    private static Field findEmbeddedIdField(Class<?> entityClass) {
        Class<?> currentClass = entityClass;

        while (currentClass != null && currentClass != Object.class) {
            Field[] fields = currentClass.getDeclaredFields();

            for (Field field : fields) {
                if (field.isAnnotationPresent(EmbeddedId.class)) {
                    field.setAccessible(true);
                    return field;
                }
            }
            currentClass = currentClass.getSuperclass();
        }
        return null;
    }

    /**
     * Composite ID with @IdClass
     * Returns the first @Id field found (there should be multiple)
     */
    private static Field findCompositeIdField(Class<?> entityClass) {
        IdClass idClassAnnotation = entityClass.getAnnotation(IdClass.class);
        if (idClassAnnotation == null) {
            return null;
        }

        Class<?> currentClass = entityClass;
        Field firstIdField = null;

        while (currentClass != null && currentClass != Object.class) {
            Field[] fields = currentClass.getDeclaredFields();

            for (Field field : fields) {
                if (field.isAnnotationPresent(Id.class)) {
                    if (firstIdField == null) {
                        field.setAccessible(true);
                        firstIdField = field;
                    }
                }
            }
            currentClass = currentClass.getSuperclass();
        }

        return firstIdField;
    }

    /**
     * Get all ID fields for composite keys
     */
    private List<Field> findAllIdFields(Class<?> entityClass) {
        List<Field> idFields = new ArrayList<>();
        Class<?> currentClass = entityClass;

        while (currentClass != null && currentClass != Object.class) {
            Field[] fields = currentClass.getDeclaredFields();

            for (Field field : fields) {
                if (field.isAnnotationPresent(Id.class) || field.isAnnotationPresent(EmbeddedId.class)) {
                    field.setAccessible(true);
                    idFields.add(field);
                }
            }
            currentClass = currentClass.getSuperclass();
        }

        return idFields;
    }

}