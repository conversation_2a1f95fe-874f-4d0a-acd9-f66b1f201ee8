package com.smaile.health.repository;

import com.smaile.health.domain.ProfessionalLinkingProjection;
import com.smaile.health.domain.ProviderProfessional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;
import java.util.UUID;

public interface ProviderProfessionalRepository extends JpaRepository<ProviderProfessional, UUID> {
    @Query("""
            SELECT DISTINCT p as professional, true as linked, :orgId as orgId
            FROM Professional p
                INNER JOIN ProviderProfessional pp ON p.id = pp.professionalId
            WHERE pp.providerId = :orgId
                AND pp.status = 'ACTIVE'
                AND p.status = 'ACTIVE'
                AND (p.fullProfessionalName ilike concat('%', coalesce(:keyword, p.fullProfessionalName), '%'))
                AND (:country is NULL OR p.country = :country)
            """)
    Page<ProfessionalLinkingProjection> findAllByLinkedMedicalProvider(UUID orgId, String keyword, String country, String speciality, Pageable pageable);


    @Query("""
            SELECT DISTINCT p as professional,
                    FALSE as linked,
                    :orgId as orgId
            FROM Professional p
            WHERE p.id NOT IN (
                        SELECT p.id
                        FROM Professional p
                            INNER JOIN ProviderProfessional pp ON p.id = pp.professionalId
                        WHERE pp.providerId = :orgId
                            AND pp.status = 'ACTIVE'
                            AND p.status = 'ACTIVE'
                )
                AND p.status = 'ACTIVE'
                AND (p.fullProfessionalName ilike concat('%', coalesce(:keyword, p.fullProfessionalName), '%'))
                AND (:country is NULL OR p.country = :country)
            """)
    Page<ProfessionalLinkingProjection> findAllUnlinkedToMedicalProvider(UUID orgId, String keyword, String country, String speciality, Pageable pageable);

    @Query("""
            SELECT DISTINCT p as professional,
                    (MAX(CASE WHEN (pp.providerId = :orgId AND pp.status = 'ACTIVE') THEN 1 ELSE 0 END) = 1) as linked,
                    :orgId as orgId
            FROM Professional p
                LEFT JOIN ProviderProfessional pp ON p.id = pp.professionalId
            WHERE p.status = 'ACTIVE'
                AND (p.fullProfessionalName ilike concat('%', coalesce(:keyword, p.fullProfessionalName), '%'))
                AND (:country is NULL OR p.country = :country)
            GROUP BY p
            """)
    Page<ProfessionalLinkingProjection> findAllWithOptionalMedicalProvider(UUID orgId, String keyword, String country, String speciality, Pageable pageable);

    Optional<ProviderProfessional> findByProviderIdAndProfessionalId(UUID actorMPId, UUID professionalId);
}
