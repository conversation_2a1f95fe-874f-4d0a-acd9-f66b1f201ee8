package com.smaile.health.repository;

import com.smaile.health.domain.Speciality;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface SpecialityRepository extends JpaRepository<Speciality, UUID>, JpaSpecificationExecutor<Speciality> {
    List<Speciality> findByIsActiveTrue();

    List<Speciality> findByNameInAndIsActiveTrue(List<String> name);

    Optional<Speciality> findByNameIgnoreCaseAndIsActiveTrue(String name);

    Optional<Speciality> findByNameIgnoreCaseAndIdNotAndIsActiveTrue(String name, UUID id);
}

