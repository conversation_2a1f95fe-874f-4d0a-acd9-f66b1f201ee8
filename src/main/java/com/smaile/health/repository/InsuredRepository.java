package com.smaile.health.repository;

import com.smaile.health.constants.InsuredStatusEnum;
import com.smaile.health.domain.Insured;
import com.smaile.health.domain.InsuredQueryProjection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;
import java.util.UUID;

public interface InsuredRepository extends JpaRepository<Insured, UUID> {
    @Query("""
            SELECT i
            FROM Insured i
                INNER JOIN InsuranceCompany ic on i.icOrg.id = ic.id
            WHERE i.insuredCode = :insuredCode
                AND i.isDeleted = FALSE
            """)
    Optional<Insured> findByIcOrgIdAndInsuredCodeAndIsDeletedFalse(UUID icOrgId, String insuredCode);

    Optional<Insured> findByIdAndIsDeletedFalse(UUID id);

    @Query("""
            SELECT i as insured, m.name as modalityName, u.username as updatedBy
            FROM Insured i
                INNER JOIN Modality m ON i.modality.id = m.id
                LEFT JOIN User u ON i.updatedBy = str(u.id)
            WHERE (
                    i.insuredCode ILIKE concat('%', :keyword, '%')
                    OR concat(i.firstName, ' ', i.lastName) ILIKE concat('%', :keyword, '%')
                    OR i.legalIdValue ILIKE concat('%', :keyword, '%')
                    OR :keyword is null
                )
                AND (:status is null OR i.status = :status)
            """)
    Page<InsuredQueryProjection> query(String keyword, InsuredStatusEnum status, Pageable pageable);
}
