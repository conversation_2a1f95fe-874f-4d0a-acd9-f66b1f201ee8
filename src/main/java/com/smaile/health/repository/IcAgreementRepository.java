package com.smaile.health.repository;

import com.smaile.health.domain.IcAgreement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.UUID;

public interface IcAgreementRepository extends JpaRepository<IcAgreement, UUID>, JpaSpecificationExecutor<IcAgreement> {

    boolean existsByAgreementIdAndInsuranceCompanyId(UUID agreementId, UUID insuranceCompanyId);
}
