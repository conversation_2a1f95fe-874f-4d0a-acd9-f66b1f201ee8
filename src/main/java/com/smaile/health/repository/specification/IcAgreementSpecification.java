package com.smaile.health.repository.specification;

import com.smaile.health.domain.*;
import com.smaile.health.model.request.Filter;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class IcAgreementSpecification {

    public static Specification<IcAgreement> search(String search) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();

            if (StringUtils.hasText(search)) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(criteriaBuilder.coalesce(root.get(IcAgreement.Fields.AGREEMENT).get(Agreement.Fields.CONTRACT_ID), "")),
                        "%" + search.toLowerCase() + "%"
                ));
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(criteriaBuilder.coalesce(root.get(IcAgreement.Fields.IC).get(InsuranceCompany.Fields.NAME), "")),
                        "%" + search.toLowerCase() + "%"
                ));
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(criteriaBuilder.coalesce(root.get(IcAgreement.Fields.AGREEMENT).get(Agreement.Fields.PROVIDER).get(MedicalProvider.Fields.NAME), "")),
                        "%" + search.toLowerCase() + "%"
                ));
            }

            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<IcAgreement> withFilter(List<Filter> filters) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();
            for (Filter filter : filters) {
                if (IcAgreement.Fields.STATUS.equals(filter.getField())) {
                    predicates.add(
                            criteriaBuilder.equal(
                                    root.get(Agreement.Fields.STATUS),
                                    filter.getValue()
                            )
                    );
                } else if (IcAgreement.Fields.IC.equals(filter.getField())) {
                    predicates.add(
                            criteriaBuilder.equal(
                                    root.get(IcAgreement.Fields.IC).get("id"),
                                    UUID.fromString(filter.getValue())
                            )
                    );

                }
            }

            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<Agreement> withChildMedicalProvider(UUID icId) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();
            if (icId != null) {
                predicates.add(criteriaBuilder.equal(root.get(Agreement.Fields.PROVIDER).get(Organization.Fields.PARENT).get(Organization.Fields.ID), icId));
            }
            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
