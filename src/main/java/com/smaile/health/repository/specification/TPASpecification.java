package com.smaile.health.repository.specification;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.domain.TPA;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class TPASpecification {
    public static Specification<TPA> searchSpecification(String country, String status, String name, String registrationNumber, String icId, OrganizationType tpaType, String adminType, UUID parentOrgId) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Ensure we only work with TPA entities (type = 'IC_TPA' or 'SMAILE_TPA')
            predicates.add(criteriaBuilder.or(
                    criteriaBuilder.equal(root.get("type"), OrganizationType.IC_TPA),
                    criteriaBuilder.equal(root.get("type"), OrganizationType.SMAILE_TPA)
            ));

            // Organization filter - filter by parent organization ID if specified
            if (parentOrgId != null) {
                predicates.add(criteriaBuilder.equal(root.get("parent").get("id"), parentOrgId));
            }

            // Country filter
            if (StringUtils.hasText(country)) {
                predicates.add(criteriaBuilder.equal(root.get("country"), country));
            }

            // Status filter
            if (StringUtils.hasText(status)) {
                try {
                    OrganizationStatus organizationStatus = OrganizationStatus.valueOf(status.toUpperCase());
                    predicates.add(criteriaBuilder.equal(root.get("status"), organizationStatus));
                } catch (IllegalArgumentException e) {
                    // If invalid status provided, return no results
                    return criteriaBuilder.disjunction();
                }
            }
            // Note: When no status filter is provided, both ACTIVE and INACTIVE records are returned

            // Name filter (case-insensitive partial match)
            if (StringUtils.hasText(name)) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("name")),
                        "%" + name.toLowerCase() + "%"
                ));
            }

            // Registration number filter (case-insensitive partial match)
            if (StringUtils.hasText(registrationNumber)) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("registrationNumber")),
                        "%" + registrationNumber.toLowerCase() + "%"
                ));
            }

            // IC ID filter - filter by parent organization ID
            if (StringUtils.hasText(icId)) {
                try {
                    UUID icUuid = UUID.fromString(icId);
                    predicates.add(criteriaBuilder.equal(root.get("parent").get("id"), icUuid));
                } catch (IllegalArgumentException e) {
                    // If invalid UUID provided, return no results
                    return criteriaBuilder.disjunction();
                }
            }

            // TPA Type filter based on user role
            if (tpaType != null) {
                predicates.add(criteriaBuilder.equal(root.get("type"), tpaType));
            }

            // Admin Type filter - filter by user role code
            if (StringUtils.hasText(adminType)) {
                // Join through the relationships: TPA -> UserOrganization -> UserRole -> Role
                Join<Object, Object> userOrgJoin = root.join("userOrganizations", JoinType.INNER);
                Join<Object, Object> userRoleJoin = userOrgJoin.join("userRoles", JoinType.INNER);
                Join<Object, Object> roleJoin = userRoleJoin.join("role", JoinType.INNER);

                // Filter by role code (case-insensitive partial match)
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(roleJoin.get("code")),
                        "%" + adminType.toLowerCase() + "%"
                ));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

}
