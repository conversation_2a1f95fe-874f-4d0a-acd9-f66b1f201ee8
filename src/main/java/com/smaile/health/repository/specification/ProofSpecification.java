package com.smaile.health.repository.specification;

import com.smaile.health.constants.Status;
import com.smaile.health.domain.Proof;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class ProofSpecification {

    public static Specification<Proof> searchSpecification(String filter) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Filter by code or description with OR condition using LIKE
            if (StringUtils.hasText(filter)) {
                String filterPattern = "%" + filter.toLowerCase() + "%";

                Predicate codePredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("code")), filterPattern);

                Predicate descriptionPredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("description")), filterPattern);

                predicates.add(criteriaBuilder.or(codePredicate, descriptionPredicate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<Proof> searchSpecificationWithStatus(String filter, Status status) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Filter by status
            if (status != null) {
                switch (status) {
                    case ACTIVE:
                        // Active: isActive = true AND (cancellationDate IS NULL OR cancellationDate >= today)
                        predicates.add(criteriaBuilder.equal(root.get("isActive"), true));
                        Predicate noCancellationDate = criteriaBuilder.isNull(root.get("cancellationDate"));
                        Predicate futureCancellationDate = criteriaBuilder.greaterThanOrEqualTo(
                                root.get("cancellationDate"), LocalDate.now());
                        predicates.add(criteriaBuilder.or(noCancellationDate, futureCancellationDate));
                        break;
                    case EXPIRED:
                        // Expired: isActive = true AND cancellationDate < today
                        predicates.add(criteriaBuilder.equal(root.get("isActive"), true));
                        predicates.add(criteriaBuilder.lessThan(root.get("cancellationDate"), LocalDate.now()));
                        break;
                    case INACTIVE:
                        // Inactive: isActive = false
                        predicates.add(criteriaBuilder.equal(root.get("isActive"), false));
                        break;
                }
            }

            // Filter by code or description with OR condition using LIKE
            if (StringUtils.hasText(filter)) {
                String filterPattern = "%" + filter.toLowerCase() + "%";

                Predicate codePredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("code")), filterPattern);

                Predicate descriptionPredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("description")), filterPattern);

                predicates.add(criteriaBuilder.or(codePredicate, descriptionPredicate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
