package com.smaile.health.repository.specification;

import com.smaile.health.domain.Procedure;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class ProcedureSpecification {

    public static Specification<Procedure> searchSpecification(String filter) {
        return (Root<Procedure> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Always filter for active procedures
            predicates.add(criteriaBuilder.equal(root.get("isActive"), true));

            // Filter by code or description with OR condition
            if (StringUtils.hasText(filter)) {
                String filterPattern = "%" + filter.toLowerCase() + "%";
                Predicate codePredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("code")), filterPattern);
                Predicate descriptionPredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("description")), filterPattern);
                predicates.add(criteriaBuilder.or(codePredicate, descriptionPredicate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

}
