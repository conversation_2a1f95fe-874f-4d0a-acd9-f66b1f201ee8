package com.smaile.health.repository.specification;

import com.smaile.health.domain.Modality;
import com.smaile.health.model.ModalitySearchCriteria;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class ModalitySpecification {

    public static Specification<Modality> search(ModalitySearchCriteria criteria, UUID orgId) {
        return (root, criteriaQuery, criteriaBuilder) -> {

            List<Predicate> orPredicates = new ArrayList<>();
            List<Predicate> andPredicates = new ArrayList<>();

            andPredicates.add(criteriaBuilder.equal(root.get("icOrg").get("id"), orgId));

            if (StringUtils.hasText(criteria.getSearch())) {
                String keyword = criteria.getSearch().toLowerCase();

                orPredicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("name")), "%" + keyword + "%")
                );

                orPredicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("shortName")), "%" + keyword + "%")
                );

                orPredicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("code")), "%" + keyword + "%")
                );

            }

            if (criteria.getNetworkType() != null) {
                andPredicates.add(criteriaBuilder.equal(root.get("networkType"), criteria.getNetworkType()));
            }

            Predicate andGroup = criteriaBuilder.and(andPredicates.toArray(new Predicate[0]));
            Predicate orGroup = orPredicates.isEmpty()
                    ? criteriaBuilder.conjunction() // always true if no OR
                    : criteriaBuilder.or(orPredicates.toArray(new Predicate[0]));

            return criteriaBuilder.and(andGroup, orGroup);
        };
    }
}
