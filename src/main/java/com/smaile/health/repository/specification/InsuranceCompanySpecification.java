package com.smaile.health.repository.specification;

import com.smaile.health.domain.InsuranceCompany;
import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class InsuranceCompanySpecification {
    public static Specification<InsuranceCompany> searchSpecificationWithOrgFilter(String market, String status, String name, UUID parentOrgId) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Ensure we only work with InsuranceCompany entities (type = 'IC')
            predicates.add(criteriaBuilder.equal(root.get("type"), OrganizationType.IC));

            // Market filter
            if (StringUtils.hasText(market)) {
                predicates.add(criteriaBuilder.equal(root.get("market"), market));
            }

            // Status filter
            if (StringUtils.hasText(status)) {
                try {
                    OrganizationStatus statusEnum = OrganizationStatus.fromString(status);
                    predicates.add(criteriaBuilder.equal(root.get("status"), statusEnum));
                } catch (IllegalArgumentException e) {
                    predicates.add(criteriaBuilder.equal(root.get("status"), OrganizationStatus.ACTIVE));
                }
            }
            // Note: When no status filter is provided, both ACTIVE and INACTIVE records are returned

            // Name filter (case-insensitive partial match)
            if (StringUtils.hasText(name)) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(criteriaBuilder.coalesce(root.get("name"), "")),
                        "%" + name.toLowerCase() + "%"
                ));
            }

            if (parentOrgId != null) {
                predicates.add(criteriaBuilder.equal(root.get("parent").get("id"), parentOrgId));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
