package com.smaile.health.repository.specification;

import com.smaile.health.domain.Agreement;
import com.smaile.health.domain.Organization;
import com.smaile.health.model.request.Filter;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class AgreementSpecification {

    public static Specification<Agreement> search(String search) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();

            if (StringUtils.hasText(search)) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(criteriaBuilder.coalesce(root.get(Agreement.Fields.CONTRACT_ID), "")),
                        "%" + search.toLowerCase() + "%"
                ));
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(criteriaBuilder.coalesce(root.get(Agreement.Fields.LONG_DESCRIPTION), "")),
                        "%" + search.toLowerCase() + "%"
                ));
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(criteriaBuilder.coalesce(root.get(Agreement.Fields.SORT_DESCRIPTION), "")),
                        "%" + search.toLowerCase() + "%"
                ));
            }

            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<Agreement> withFilter(List<Filter> filters) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();
            for (Filter filter : filters) {
                if (Agreement.Fields.STATUS.equals(filter.getField())) {
                    predicates.add(criteriaBuilder.equal(root.get(Agreement.Fields.STATUS), filter.getValue()));
                } else if (Agreement.Fields.PROVIDER_ID.equals(filter.getField())) {
                    predicates.add(
                            criteriaBuilder.equal(
                                root.get(Agreement.Fields.PROVIDER).get("id"),
                                UUID.fromString(filter.getValue())
                            )
                    );
                }
            }

            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<Agreement> withChildMedicalProvider(UUID icId) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();
            if (icId != null) {
                predicates.add(criteriaBuilder.equal(root.get(Agreement.Fields.PROVIDER).get(Organization.Fields.PARENT).get(Organization.Fields.ID), icId));
            }
            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
