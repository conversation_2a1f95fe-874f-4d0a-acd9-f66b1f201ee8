package com.smaile.health.repository.specification;

import com.smaile.health.domain.Diagnosis;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import jakarta.persistence.criteria.Predicate;

import java.util.ArrayList;
import java.util.List;

public class DiagnosisSpecification {

    public static Specification<Diagnosis> searchSpecification(String filter) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Always filter for active diagnoses
            predicates.add(criteriaBuilder.equal(root.get("isActive"), true));

            // Filter by code or description with OR condition
            if (StringUtils.hasText(filter)) {
                String filterPattern = "%" + filter.toLowerCase() + "%";
                Predicate codePredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("code")), filterPattern);
                Predicate descriptionPredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("description")), filterPattern);
                predicates.add(criteriaBuilder.or(codePredicate, descriptionPredicate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
