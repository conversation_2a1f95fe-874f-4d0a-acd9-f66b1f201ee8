package com.smaile.health.repository.specification;

import com.smaile.health.domain.Agreement;
import com.smaile.health.domain.MedicalProvider;
import com.smaile.health.model.request.Filter;
import jakarta.persistence.criteria.Predicate;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class MedicalProviderSpecification {

    public static Specification<MedicalProvider> search(String search) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();

            if (StringUtils.hasText(search)) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(criteriaBuilder.coalesce(root.get("name"), "")),
                        "%" + search.toLowerCase() + "%"
                ));
            }

            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<MedicalProvider> withParentId(UUID parentId) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();
            if (parentId != null) {
                predicates.add(
                        criteriaBuilder.equal(
                                root.get("parent").get("id"), parentId
                        )
                );
            }

            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

}
