package com.smaile.health.repository.specification;

import com.smaile.health.domain.Speciality;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class SpecialitySpecification {

    public static Specification<Speciality> searchSpecification(String filter) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Always filter for active specialties
            predicates.add(criteriaBuilder.equal(root.get("isActive"), true));

            // Filter by name or description with OR condition using LIKE
            if (StringUtils.hasText(filter)) {
                String filterPattern = "%" + filter.toLowerCase() + "%";

                // Search in name field
                Predicate namePredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("name")), filterPattern);

                // Search in description field
                Predicate descriptionPredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("description")), filterPattern);

                // Use OR condition between name and description
                predicates.add(criteriaBuilder.or(namePredicate, descriptionPredicate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}