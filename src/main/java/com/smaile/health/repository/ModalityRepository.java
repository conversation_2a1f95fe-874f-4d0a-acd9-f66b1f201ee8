package com.smaile.health.repository;

import com.smaile.health.domain.Modality;
import com.smaile.health.domain.Organization;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface ModalityRepository extends JpaRepository<Modality, UUID>, JpaSpecificationExecutor<Modality> {

    Optional<Modality> findByCode(String code);

    boolean existsByIdAndIcOrg_Id(UUID id,  UUID icOrgId);
}
