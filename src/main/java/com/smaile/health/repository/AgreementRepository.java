package com.smaile.health.repository;

import com.smaile.health.domain.Agreement;
import com.smaile.health.domain.MedicalProvider;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

public interface AgreementRepository extends JpaRepository<Agreement, UUID>, JpaSpecificationExecutor<Agreement> {

    boolean existsByContractId(String contractId);

    @Query("""
            SELECT a
            FROM Agreement a
                LEFT JOIN FETCH a.proceduresDetails pd
                LEFT JOIN FETCH pd.procedure
            WHERE a.id = :id
            """)
    Optional<Agreement> findById(@Param("id") UUID id);

    List<Agreement> findAllByContractId(String contractId);

    @Query("""
            SELECT a.provider.id, COUNT(a)
            FROM Agreement a
            WHERE a.provider.id IN :medicalProviderIds
            GROUP BY a.provider.id
            """)
    List<Object[]> countByProviderRaw(@Param("medicalProviderIds") List<UUID> medicalProviderIds);

    // Default method to convert to Map
    default Map<UUID, Long> countByProvider(List<UUID> medicalProviderIds) {
        List<Object[]> raw = countByProviderRaw(medicalProviderIds);
        return raw.stream().collect(Collectors.toMap(row -> (UUID) row[0], row -> (Long) row[1]));
    }

    @Query("""
            SELECT p
            FROM Agreement a
            JOIN a.provider p
            WHERE a.id = :agreementId
            """)
    List<MedicalProvider> getAllMedicalProviderBy(UUID agreementId);

    List<Agreement> findAllByProviderId(UUID medicalProviderId);

}
