package com.smaile.health.repository;

import com.smaile.health.domain.AuthorizedSpecialty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface AuthorizedSpecialtyRepository extends JpaRepository<AuthorizedSpecialty, UUID>, JpaSpecificationExecutor<AuthorizedSpecialty> {
    List<AuthorizedSpecialty> findByIsActiveTrue();
}
