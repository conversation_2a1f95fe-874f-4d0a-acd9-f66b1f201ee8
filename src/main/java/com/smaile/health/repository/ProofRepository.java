package com.smaile.health.repository;

import com.smaile.health.domain.Proof;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ProofRepository extends JpaRepository<Proof, UUID>, JpaSpecificationExecutor<Proof> {
    List<Proof> findByIsActiveTrue();

    Optional<Proof> findByCodeIgnoreCase(String code);

    Optional<Proof> findByCodeIgnoreCaseAndIdNot(String code, UUID id);
}
