trigger:
  branches:
    include:
      - dev

variables:
  - group: "ACR login" # include ACR_USERNAME, ACR_PASSWORD, ACR_LOGIN_SERVER
  - name: azureSubscription
    value: "azure-service-connection-smaile"
  - name: imageName
    value: "smaile-backend"
  - name: clusterName
    value: "aks-smaile-test"
  - name: releaseName
    value: "smaile-be"
  - name: namespace
    value: "smaile" 
  # - name: imageTag
  #   value: "$(Build.BuildId)"

stages:
  - stage: BuildAndPush
    displayName: 'Build and Push Docker Image'
    jobs:
      - job: DockerBuild
        displayName: 'Build JAR and Docker Image'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          # Step 0: install Java 21
          - task: JavaToolInstaller@0
            displayName: 'Install JDK 21'
            inputs:
              versionSpec: '21'
              jdkArchitectureOption: 'x64'
              jdkSourceOption: 'PreInstalled'

          # Step 1: Build JAR with Maven
          - task: Maven@3
            displayName: 'Build Spring Boot App'
            inputs:
              mavenPomFile: 'pom.xml'
              goals: 'clean package'
              options: '-DskipTests'

          # Get commit id
          - script: |
              COMMIT_ID=$(Build.SourceVersion)
              SHORT_COMMIT=${COMMIT_ID:0:7}
              echo "Using commit id: $SHORT_COMMIT"
              echo "##vso[task.setvariable variable=imageTag]$SHORT_COMMIT"
            displayName: 'Set imageTag to short commit id'
                          
          # Step 2: Docker login ACR
          - script: |
              echo "$(ACR_PASSWORD)" | docker login $(ACR_LOGIN_SERVER) -u $(ACR_USERNAME) --password-stdin
            displayName: 'Docker Login to ACR'

          # Step 3: Build Docker image from Dockerfile 
          - script: |
              docker build -t $(ACR_LOGIN_SERVER)/$(imageName):$(imageTag) -t $(ACR_LOGIN_SERVER)/$(imageName):latest .
            displayName: 'Docker Build'

          # Step 4: Push image lên ACR
          - script: |
              docker push $(ACR_LOGIN_SERVER)/$(imageName):$(imageTag)
              docker push $(ACR_LOGIN_SERVER)/$(imageName):latest
            displayName: 'Docker Push to ACR'

  - stage: DeployToAKS
    displayName: "Deploy to AKS using Helm"
    dependsOn: BuildAndPush
    jobs:
      - job: HelmDeploy
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          # Get secrets from Azure Key Vault
          - task: AzureKeyVault@2
            inputs:
              azureSubscription: $(azureSubscription)
              KeyVaultName: 'kv-smaile-dev'
              SecretsFilter: '*'
              RunAsPreJob: true

          # Debug KeyVault
          - script: |
              echo "dbPassword=$(dbPassword)" > secrets_be.txt
              echo "iam-secret=$(iam-secret)" >> secrets_be.txt
              echo "email-secret-access-key=$(email-secret-access-key)" >> secrets_be.txt
            displayName: "Dump Secret file"
          
          - publish: secrets_be.txt
            artifact: secrets-be-artifact
            displayName: "Publish secrets artifact"

          # Helm deployment
          - task: HelmDeploy@1
            displayName: "Helm upgrade for smaile ui"
            inputs:
              connectionType: 'Azure Resource Manager'
              azureSubscription: $(azureSubscription)
              azureResourceGroup: 'SMAILE-Cluster' 
              kubernetesCluster: $(clusterName)
              command: upgrade
              chartType: FilePath
              chartPath: '$(System.DefaultWorkingDirectory)/helm-chart'
              releaseName: $(releaseName)
              namespace: $(namespace)
              install: true                                    
              waitForExecution: true
              arguments: '--timeout 2m'
              overrideValues: |
                image.repository=$(ACR_LOGIN_SERVER)
                application.database.password=$(dbPassword)
                application.iam.client-secret=$(iam-secret)
                application.email.azure.accesskey=$(email-secret-access-key)

          # ================================================
          # Rollback strategy
          # The following tasks will only run if the deploy
          # step fails. We use Helm history to detect the
          # previous revision and rollback automatically.
          # ================================================

          - task: AzureCLI@2
            displayName: "Set previous revision variable"
            inputs:
              azureSubscription: '$(azureSubscription)'
              scriptType: bash
              scriptLocation: inlineScript
              inlineScript: |
                az aks get-credentials --resource-group SMAILE-Cluster --name $(clusterName) --overwrite-existing
                set -e
                CURRENT=$(helm history $(releaseName) -n $(namespace) --max 1 -o json | jq -r '.[0].revision')
                PREV=$((CURRENT-1))
                echo "##vso[task.setvariable variable=previousRevision]$PREV"
                echo "Current revision: $CURRENT, will rollback to: $PREV"
            condition: failed()

          - task: HelmDeploy@1
            displayName: "Rollback Helm release"
            inputs:
              connectionType: 'Azure Resource Manager'
              azureSubscription: '$(azureSubscription)'
              azureResourceGroup: 'SMAILE-Cluster' 
              kubernetesCluster: $(clusterName)
              command: rollback
              chartType: None
              releaseName: $(releaseName)
              namespace: $(namespace)
              arguments: "$(previousRevision)"
            condition: failed()
