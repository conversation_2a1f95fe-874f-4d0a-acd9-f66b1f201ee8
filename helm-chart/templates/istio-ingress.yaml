apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: {{ include "chart.name" . }}-gateway
spec:
  selector:
    istio: ingressgateway
  servers:
    - port:
        number: 80
        name: http
        protocol: HTTP
      hosts:
        - {{ .Values.global.subDomain }}.{{ .Values.global.baseDomain }}
      tls:
        httpsRedirect: true
    - port:
        number: 443
        name: https
        protocol: HTTPS
      tls:
        mode: SIMPLE
        credentialName: smaile-api-tls
      hosts:
        - {{ .Values.global.subDomain }}.{{ .Values.global.baseDomain }}
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: {{ include "chart.name" . }}
spec:
  gateways:
    - {{ .Chart.Name }}-gateway
  hosts:
    - {{ .Values.global.subDomain }}.{{ .Values.global.baseDomain }}
  http:
    - match:
        - uri:
            prefix: "/api/"
      route:
        - destination:
            host: {{ .Chart.Name }}.{{ .Release.Namespace }}.{{ .Values.clusterDomain }}
            port:
              number: 80
---
apiVersion: security.istio.io/v1beta1
kind: RequestAuthentication
metadata:
  name: {{ include "chart.name" . }}-request-authentication
spec:
  selector:
    matchLabels:
      app: {{ include "chart.name" . }}
  jwtRules:
    - issuer: "https://iam.{{ .Values.global.baseDomain }}/realms/{{ .Values.application.iam.realm }}"
      jwksUri: "https://iam.{{ .Values.global.baseDomain }}/realms/{{ .Values.application.iam.realm }}/protocol/openid-connect/certs"
      forwardOriginalToken: true
      outputClaimToHeaders:
        - header: "{{ .Values.keycloakUserId.header }}"
          claim: "{{ .Values.keycloakUserId.claim }}"
---
# Allow swagger-ui without JWT
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: {{ include "chart.name" . }}-swagger-allow
spec:
  selector:
    matchLabels:
      app: {{ include "chart.name" . }}
  action: ALLOW
  rules:
    - to:
        - operation:
            paths: ["/api/v1/swagger-ui*", "/api/v1/v3/api-docs*", "/api/v1/register/*", "/api/v1/utilities/*"]

---
# Require JWT for all other API v1 paths
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: {{ include "chart.name" . }}-auth
spec:
  selector:
    matchLabels:
      app: {{ include "chart.name" . }}
  action: ALLOW
  rules:
    - to:
        - operation:
            methods:
              - OPTIONS
    - when:
        - key: request.auth.claims[iss]
          values: ["https://iam.{{ .Values.global.baseDomain }}/realms/{{ .Values.application.iam.realm }}"]
      to:
        - operation:
            paths: ["/api/v1/*"]