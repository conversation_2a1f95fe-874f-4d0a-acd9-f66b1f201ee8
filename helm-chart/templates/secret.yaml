# The secret of this service can be defined here
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "chart.name" . }}-db-secret
type: Opaque

stringData:
  password: {{ .Values.application.database.password }}

---

# The secret of this service can be defined here
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "chart.name" . }}-iam-secret
type: Opaque

stringData:
  client-secret: {{ .Values.application.iam.client.secret }}

---

apiVersion: v1
kind: Secret
metadata:
  name: {{ include "chart.name" . }}-email-secret
type: Opaque

stringData:
  access-key: {{ .Values.application.email.azure.accesskey }}